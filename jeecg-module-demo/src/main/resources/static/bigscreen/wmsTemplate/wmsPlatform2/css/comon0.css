@charset "utf-8";
/* CSS Document */
*{
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box}
*,body{padding:0px;	margin:0px;color: #fff;;font-family: "微软雅黑";}
/*body{ background: linear-gradient(25deg, #0f2249,#182e5a 20%,#0f2249 40%,#182e5a 60%,#0f2249 80%,#182e5a 100%);color:#fff;font-size: .1rem;}*/
body{ background:#172c57;color:#fff;font-size: .1rem;}
li{ list-style-type:none;}
table{}
i{ margin:0px; padding:0px; text-indent:0px;}
img{ border:none; max-width: 100%;}
a{ text-decoration:none; color:#399bff;}
a.active,a:focus{ outline:none!important; text-decoration:none;}
ol,ul,p,h1,h2,h3,h4,h5,h6{ padding:0; margin:0}
a:hover{ color:#06c; text-decoration: none!important}
i,em{font-style: normal;}

.clearfix:after, .clearfix:before {
	display: table;
	content: " "
}
.clearfix:after {
	clear: both
}
.pulll_left{float:left;}
.pulll_right{float:right;}
/*谷哥滚动条样式*/

::-webkit-scrollbar {width:5px;height:5px;position:absolute}
::-webkit-scrollbar-thumb {background-color:#5bc0de}
::-webkit-scrollbar-track {background-color:#ddd}

/***/

.loading{position:fixed; left:0; top:0; font-size:.3rem; z-index:100000000;width:100%; height:100%; background:#1a1a1c; text-align:center;}
.loadbox{position:absolute; width:160px;height:150px; color: #324e93; left:50%; top:50%; margin-top:-100px; margin-left:-75px;}
.loadbox img{ margin:10px auto; display:block; width:40px;}


.head{ height:calc(.8rem);  background: url(../images/head_bg.png) no-repeat center center; background-size: 100% 100%; position: relative}
.head h1{ color:#bde4ff; text-align: center; font-size: .4rem; line-height:0.8rem; letter-spacing: .06rem;}
.head h1 img{ width:1.5rem; display: inline-block; vertical-align: middle; margin-right: .2rem}
/** showTime -- START */
.nowTime {
	position: absolute;
	right: 20px;
	top: 5px;
	font-size: 0;
}

.nowTime li {
	display: inline-block;
	min-width: 140px;
	height: 40px;
	font-size: 30px;
	color: #fff;
}

.nowTime li {
	display: inline-block;
	float: left;
	/*font-weight: 800;*/
	background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.nowTime li div {
	background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	font-size: 16px;
	text-align: left;
}

.nowTime li p {
	background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#fff), to(#5ec0d2));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	font-size: 16px;
}

html,body{height: 100%;overflow-y: hidden;}
.mainbox{ padding:0.15rem 0rem 0rem 0rem; height:calc(100% - .8rem); }
.mainbox>ul{ margin-left:0.02rem; margin-right:0.02rem; height: 100%;}
.mainbox>ul>li{ float: left; padding: 0 .05rem;height: 100%;}
.mainbox>ul>li{ width: 25%}
.mainbox>ul>li:nth-child(2){ width: 75%}
/*padding:.12rem;*/
.boxall{ border: 1px solid #3486da; background: rgba(0,70,190,.1); position: relative; margin-bottom: .12rem; z-index: 10;}
.boxall:before,
.boxall:after{ position:absolute; width: .15rem; height: .15rem; content: "";  border-top: 3px solid #3486da; top: -2px;}
.boxall:before,.boxfoot:before{border-left: 3px solid #3486da;left:-2px;}
.boxall:after,.boxfoot:after{border-right: 3px solid #3486da; right: -2px;}

.boxfoot{ position:absolute; bottom: 0; width: 100%; left:0;}
.boxfoot:before,
.boxfoot:after{ position:absolute; width: .15rem; height: .15rem;  content: "";border-bottom:3px solid #3486da; bottom:-2px;}
.boxall .tit01:before{
	content: "";
	background-image: url(../images/head_icon.svg);
	background-size: contain;
	/*display: inline-block;*/
	float: left;
	width: .2rem;
	height: .2rem;
	margin-top: 1px;
	margin-right: 3px;
}
.boxall .tit04:before{
	content: "";
	background-image: url(../images/renyuan.svg);
	background-size: contain;
	/*display: inline-block;*/
	float: left;
	width: .2rem;
	height: .2rem;
	margin-left: .2rem;
	margin-top: 2px;
	margin-right: 3px;
}
.tit01{background: linear-gradient(to right,rgba(48,82,174,1),rgba(48,82,174,0)); color: #fff; font-size: .17rem; padding: .01rem .1rem;}
.tit02{ background: #3aafe8;  color: #021132; position: absolute; font-size: .25rem; padding: .02rem 0; text-align: center; width: 2.4rem; left: 50%; margin-left: -1.2rem; margin-top: -.35rem; letter-spacing: .05rem;}
.tit03{text-align: left; background: linear-gradient(to right,rgba(48,82,174,1),rgba(48,82,174,0)); color: #fff;font-size: .18rem; line-height: .4rem; padding: .03rem .1rem; letter-spacing: .02rem;}
.tit04{ background: linear-gradient(to right,rgba(48,82,174,1),rgba(48,82,174,0)); color: #fff;font-size: .16rem; padding: 0 0 0 0; line-height: .25rem; letter-spacing: .0rem;}

.wrap{ height:2.54rem; overflow: hidden;}
.wrap li{  line-height:.42rem; height:.42rem; font-size: .18rem; text-indent: .24rem; margin-bottom: .1rem; }
.wrap li p{border: 1px solid rgba(25,186,139,.17);color: rgba(255,255,255,.6); }

.boxnav{}
.nav01{ height: calc(100% - .1rem); padding-top: .06rem;}
.nav02{ height: calc(100%); display: flex; align-items: center;}
.nav03{ height: calc(100% - .2rem); padding-top: .13rem;}
.nav04{ height: calc(100% - .15rem);}
.nav02>div{width: 100%;}
.nav02 p{text-align: center; color: #fff;}
.nav02 .p1{font-size: .3rem; letter-spacing: .02rem;}
.p2{font-size: .70rem; letter-spacing: .05rem; padding: 0 0 0 0; width: 100%; text-align:center; vertical-align: bottom;}
.p4{font-size: .60rem; letter-spacing: .05rem; padding: 0 0 0 0; width: 100%; text-align:left; vertical-align: bottom;}
.nav02 .p3 span{text-align: center; color: #fff; display: inline-block; width: 45%;font-size: .3rem;}
.dot{width: .2rem;height: .2rem; display: inline-block; border-radius: 1rem; position: relative; top: .04rem; margin-right: .1rem; }
.dot1{background: #ff0006}
.dot2{background: #00ea37}
.text-green{ color: #00ea37}
.text-yellow{color: #EEC900}
.text-red{ color: #ff0006;}
.text-h{ color: #8B8682;}
.text-blue{ color: #0000CD;}
.listhead{height: .35rem;display: flex; justify-content: left; }
.listnav{height:calc(100% - .55rem); }
.listnav ul li{height: 100%; display: flex; padding: .01rem .03rem; justify-content: left;}
.listhead span,.listnav li span{width: 100%; align-items: center; justify-content: left; padding: .01rem .03rem; display: flex;height: .40rem;}
.listhead1 span,.listnav1 li span{border: 0.5px solid #3486da;}
.listnav ul li span{ font-size:.15rem;}
.listnav2 ul li:nth-child(odd){ background: #0c2854; }
.listnav3 ul li:nth-child(odd){ background: #0c2854;}
/* course type time content */
.listnav2 ul li span:nth-child(1){
	flex-basis: 15%;
}
.listnav2 ul li span:nth-child(2){
	flex-basis: 11%;
}
.listnav2 ul li span:nth-child(3){
	flex-basis: 10%;
}
.listnav2 ul li span:nth-child(4){
	flex-basis: 30%;
}
.listnav2 ul li span:nth-child(5){
	flex-basis: 13%;
}
.listnav2 ul li span:nth-child(6){
	flex-basis: 13%;
}
.listnav2 ul li span:nth-child(7){
	flex-basis: 8%;
}
.listhead1 span{background: #0c2854; font-size: .16rem; color: #3486da; font-weight: bold;}
.listhead2 span{ font-size: .17rem; color: #3486da; border-bottom: 1px solid #0c2854; font-weight: bold;}

.listhead3 span{ font-size: .17rem; color: #3486da; border-bottom: 1px solid #0c2854; font-weight: bold;}
/*.boxall:nth-child(1){*/
/*	flex-basis: 80%;*/
/*}*/
.listhead2 span:nth-child(1){
	flex-basis: 15%;
}
.listhead2 span:nth-child(2){
	flex-basis: 11%;
}
.listhead2 span:nth-child(3){
	flex-basis: 10%;
}
.listhead2 span:nth-child(4){
	flex-basis: 30%;
}
.listhead2 span:nth-child(5){
	padding-left: .05rem;
	flex-basis: 12%;
}
.listhead2 span:nth-child(6){
	padding-left: .05rem;
	flex-basis: 12%;
}
.listhead2 span:nth-child(7){
	flex-basis: 10%;
}





/*Plugin CSS*/
.str_wrap {
	overflow:hidden;
	width:100%;
	position:relative;
	-moz-user-select: none;
	-khtml-user-select: none;
	user-select: none;
	white-space:nowrap;
}


.str_move {
	white-space:nowrap;
	position:absolute;
	top:0;
	left:0;
	cursor:move;
}
.str_move_clone {
	display:inline-block;
	vertical-align:top;
	position:absolute;
	left:100%;
	top:0;
}
.str_vertical .str_move_clone {
	left:0;
	top:100%;
}
.str_down .str_move_clone {
	left:0;
	bottom:100%;
}
.str_vertical .str_move,
.str_down .str_move {
	white-space:normal;
	width:100%;
}
.str_static .str_move,
.no_drag .str_move,
.noStop .str_move{
	cursor:inherit;
}
.str_wrap img {
	max-width:none !important;
}

.payment-time{
	padding-right: 10px;
}


.mainbox-outbound {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.header-section {
	min-height: calc(23% - .55rem);
	background-color: #152F61;
	padding: .20rem .40rem;
	border-bottom: 1px solid #ddd;
}

.header-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.2rem;
}

.header-item {
	flex: 1;
	min-width: 45%; /* 确保有足够的空间 */
	margin-right: 2%; /* 为右边元素留出一些空间 */
	display: flex; /* 使用 Flexbox 布局 */
	align-items: center; /* 垂直居中对齐 */
}

.header-item:last-child {
	margin-right: 0; /* 最后一个元素不需要右边距 */
}

.header-item .label {
	font-weight: bold;
	font-size: .50rem; /* 加大字体 */
	margin-right: .15rem; /* 为值留出一些空间 */
}

.header-item .value {
	font-size: .50rem; /* 加大字体 */
	margin: 0; /* 移除默认的 p 标签边距 */
}

.table-container {
	/*flex-grow: 1;*/
	max-height: calc(72% - .55rem); /* 设置最大高度 */
	overflow-y: hidden; /* 隐藏滚动条 */
	overflow-x: hidden; /* 隐藏滚动条 */
	position: relative;
}

.data-table {
	max-height: calc(72% - .55rem); /* 设置最大高度 */
	width: 100%;
	border-collapse: collapse;
}

.data-table th, .data-table td {
	/*border: 1px solid #ddd;*/
	box-sizing: border-box;
	padding: .15rem;
	text-align: left;
	font-size: .50rem; /* 加大字体 */
}

.data-table tr:nth-child(odd) {
	background-color: #0c2854;
}

.data-table th {
	height: calc(10% - .55rem);
	font-size: .50rem; /* 加大字体 */
	position: sticky;
	top: -1px; /* 确保表头固定在顶部 */
	background-color: #152F61;
	white-space: nowrap;
}

.data-table thead {
	border-bottom: 2px solid black; /* 设置底部边框为黑色 */
}

.mainbox .tit01:before{
	content: "";
	background-image: url(../images/head_icon.svg);
	background-size: contain;
	/*display: inline-block;*/
	float: left;
	width: .2rem;
	height: .2rem;
	margin-top: 1px;
	margin-right: 3px;
}