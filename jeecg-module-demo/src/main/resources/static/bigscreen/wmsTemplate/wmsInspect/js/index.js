$(document).ready(function () {
    // 设置根元素的字体大小，基于窗口宽度
    updateFontSize();
    $(window).resize(updateFontSize);

    // 设置看板标题和系统配置信息
    setBoardTitleAndSysConfig();

    // 初始化看板数据
    initBoardData();

    // 开始显示当前时间，并每秒更新一次
    updateCurrentTime();
});

const prefix = window.location.protocol + '//' + window.location.host + '/jeecg-boot';

// 动态调整根元素的字体大小
function updateFontSize() {
    var windowWidth = $(window).width();
    $("html").css({ fontSize: windowWidth / 20 });
}

function getPlatformCode() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('platformCode');
}

// 设置看板标题和系统配置信息
function setBoardTitleAndSysConfig() {
    const platformCode = getPlatformCode();  // 获取platformCode作为站台号
    // 获取配置信息
    $.ajax({
        url: prefix + "/bigScreen/listAll/sysConfig",
        type: 'GET',
        async: false,
        dataType: 'json',
        success: function (res) {
            if (res) {
                const sysConfigData = res;
                if (sysConfigData) {
                    // document.title = `站台号：${platformCode} - ${sysConfigData.companyName}`;
                    $("#sysConfig").html(`<h1>检验信息数字看板</h1>`);
                    $("#overlay").html(`
                        <h1>${sysConfigData.companyName}</h1>
                    `);
                    $("#overlay").append(`<div id="currentTime"></div>`);

                    // 设置看板刷新时间
                    let refreshInterval = 10000; // 默认刷新间隔为5秒

                    // 每隔指定时间刷新一次看板数据
                    setInterval(initBoardData, refreshInterval);

                } else {
                    document.title = `站台号：${platformCode}`;
                    $("#sysConfig").html("<h1>站台信息看板</h1>");
                    console.warn("公司简称获取失败！");
                }
            }
        }
    });
}

// 获取并显示当前时间，每秒更新一次
function updateCurrentTime() {
    function getNowFormatDate() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        const second = String(date.getSeconds()).padStart(2, '0');
        const showDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const dayName = showDay[date.getDay()];

        const timeString = `${hour}:${minute}:${second}`;
        const currentDate = `<div><p>${year}年${month}月${day}日 ${timeString} ${dayName}</p></div>`;
        $('.nowTime li:nth-child(2)').html(currentDate);
        $("#currentTime").html(currentDate);
        setTimeout(getNowFormatDate, 1000); // 每秒更新一次时间
    }
    getNowFormatDate();
}

// 初始化看板数据
function initBoardData() {
    $.ajax({
        url: prefix + "/wms/pickTask/getCheckInfo",
        type: 'GET',
        async: false,
        dataType: 'json',
        success: function (res) {
            if (res) {
                console.log("看板数据加载成功：", res);
                // 隐藏遮挡层并显示主要内容
                $("#overlay").css('display', 'none');
                const data = res;

                // 仅显示出库模式页面
                $(".mainbox-outbound").css('display', 'block');

                // 填充出库数据
                const billInformation = `检验单号:${data.billNo || ''}  采购订单:${data.purchaseNo || ''}`;
                // const itemName = `${data.itemName || ''}-${data.batch_spec || ''}`;

                $("#departInfo").html(`<span>${data.inspectResult || ''}/${data.processResult || ''}</span>`);
                $("#billInformation").html(`<span>${billInformation}</span>`);
                $("#merchantName").html(`<span>${data.inspectMode || ''}</span>`);
                $("#itemCode").html(`<span>${data.itemCode || ''}</span>`);
                $("#itemName").html(`<span>${data.itemName}</span>`);
                $("#sendSchedule").html(`
    <span>
    检验数量: <span>${data.inspectQty || 0}</span>/
        合格数量: <span style="color: #aaff00;">${data.standardQty || 0}</span>  
    </span>
`);

            } else {
                // 显示遮挡层并隐藏主要内容
                $("#overlay").css('display', 'block');
                $(".mainbox-outbound").css('display', 'none');
            }
        },
        error: function (xhr, status, error) {
            console.error("数据加载失败：", error);
            // 显示遮挡层并隐藏主要内容
            $("#overlay").css('display', 'block');
            $(".mainbox-outbound").css('display', 'none');
        }
    });
}
