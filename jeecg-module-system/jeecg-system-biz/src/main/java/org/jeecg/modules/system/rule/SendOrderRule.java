package org.jeecg.modules.system.rule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.YouBianCodeUtil;
import org.jeecg.modules.admin.entity.WmsSend;
import org.jeecg.modules.admin.service.IWmsSendService;
import org.jeecg.modules.system.util.LocalDateTimeUtils;

import java.util.List;

/**
 * 出库单（流水号）
 */
public class SendOrderRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        // 获取 wmsSendService 实例
        IWmsSendService recordService = (IWmsSendService) SpringContextUtils.getBean("wmsSendServiceImpl");

        // 默认前缀及位数
        String prefix = "CKD"; // 出库单默认前缀，可根据需要自定义
        int digit = 4;  // 默认编号后缀位数

        if (params != null) {
            Object prefixStr = params.get("prefix");
            if (prefixStr != null) {
                prefix = prefixStr.toString();
            }

            Object digitStr = params.get("digit");
            if (digitStr != null) {
                digit = Integer.parseInt(digitStr.toString());
            }
        }

        // 日期格式化
        String timeRule = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.YYYYMMDD);
        // 初始化编号样例
        String isbn = prefix + timeRule + String.format("%0" + digit + "d", 0);

        // 查询当天已有的最大编号
        LambdaQueryWrapper<WmsSend> query = new LambdaQueryWrapper<>();
        query.select(WmsSend::getBillNo)
                .likeRight(WmsSend::getBillNo, prefix + timeRule) // 按前缀+日期匹配
                .orderByDesc(WmsSend::getBillNo)
                .last("LIMIT 1");
        List<Object> list = recordService.listObjs(query);
        if (list != null && list.size() > 0) {
            isbn = (String) list.get(0);
        }

        // 使用工具类生成下一个编号
        return YouBianCodeUtil.getNextYouBianCode(isbn);
    }
}
