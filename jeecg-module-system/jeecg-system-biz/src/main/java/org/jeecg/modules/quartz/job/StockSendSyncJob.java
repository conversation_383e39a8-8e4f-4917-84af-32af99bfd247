package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.ISapApiService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 库存发货单同步定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class StockSendSyncJob implements Job {

    @Autowired
    private ISapApiService sapApiService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("开始同步库存发货单到SAP");
//            sapApiService.syncStockSend();
            log.info("库存发货单同步完成");
        } catch (Exception e) {
            log.error("库存发货单同步失败", e);
            throw new JobExecutionException(e);
        }
    }
}
