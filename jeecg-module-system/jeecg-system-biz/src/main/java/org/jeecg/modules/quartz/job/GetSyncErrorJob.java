package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.ISapApiService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 生产收货单同步定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class GetSyncErrorJob implements Job {

    @Autowired
    private ISapApiService sapApiService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("开始获取过去24小时同步失败的数据");
            sapApiService.getSyncError();
            log.info("开始获取过去24小时同步失败的数据成功");
        } catch (Exception e) {
            log.error("开始获取过去24小时同步失败的数据失败", e);
            throw new JobExecutionException(e);
        }
    }
}
