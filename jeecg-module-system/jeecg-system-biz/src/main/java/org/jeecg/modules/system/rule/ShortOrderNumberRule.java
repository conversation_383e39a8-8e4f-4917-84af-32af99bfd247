package org.jeecg.modules.system.rule;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.handler.IFillRuleHandler;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 填值规则Demo：生成订单号
 * 【测试示例】
 */
public class ShortOrderNumberRule implements IFillRuleHandler {

    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        String prefix = "";
        // 如果规则参数不为空，且包含自定义前缀，则使用自定义前缀
        if (params != null && params.containsKey("prefix")) {
            prefix = params.getString("prefix");
        }

        // 生成最多10位的随机数字，范围从0到9999999999
        long randomNumber = ThreadLocalRandom.current().nextLong(0, 100000000L);
        String randomNumberStr = String.format("%010d", randomNumber);

        String value = prefix + randomNumberStr;

        // 根据formData的值的不同，生成不同的订单号
        String name = formData.getString("name");
        if (StringUtils.isNotEmpty(name)) {
            value += name;
        }
        return value;
    }

}
