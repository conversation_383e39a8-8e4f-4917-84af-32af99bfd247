package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.ISapApiService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 销售交货草稿同步定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class SalesDraftJob implements Job {

    @Autowired
    private ISapApiService sapApiService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("开始同步销售交货草稿");
            sapApiService.getSalesdraft();
            log.info("销售交货草稿同步完成");
        } catch (Exception e) {
            log.error("销售交货草稿同步失败", e);
            throw new JobExecutionException(e);
        }
    }
}
