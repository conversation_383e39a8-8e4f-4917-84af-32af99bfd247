package org.jeecg.modules.system.rule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.YouBianCodeUtil;
import org.jeecg.modules.admin.entity.WmsStockDump;
import org.jeecg.modules.admin.service.IWmsStockDumpService;
import org.jeecg.modules.system.util.LocalDateTimeUtils;

import java.util.List;

/**
 * 库存转储单流水号生成规则
 */
public class StockDumpOrderRule implements IFillRuleHandler {

    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        // 1. 获取库存转储单服务（确保 service Bean 已注册到 Spring 容器中）
        IWmsStockDumpService dumpService = (IWmsStockDumpService) SpringContextUtils.getBean("wmsStockDumpServiceImpl");

        // 2. 设置默认参数：默认前缀和数字位数
        // 默认前缀设为 "KCTZD"（库存转储单缩写），数字部分默认为4位
        String prefix = "ZCD";
        int digit = 4;
        if (params != null) {
            Object prefixStr = params.get("prefix");
            if (prefixStr != null) {
                prefix = prefixStr.toString();
            }
            Object digitStr = params.get("digit");
            if (digitStr != null) {
                digit = Integer.parseInt(digitStr.toString());
            }
        }

        // 3. 构造流水号：基于当前日期和初始数字 0 拼接生成基础编号
        String timeRule = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.YYYYMMDD);
        String billNo = prefix + timeRule + String.format("%0" + digit + "d", 0);

        // 4. 查询当天已生成的最新库存转储单流水号（按 billNo 模糊匹配当天的记录，并取最大值）
        LambdaQueryWrapper<WmsStockDump> query = new LambdaQueryWrapper<>();
        query.select(WmsStockDump::getBillNo)
                .likeRight(WmsStockDump::getBillNo, prefix + timeRule) // 查找以前缀+日期开头的流水号
                .orderByDesc(WmsStockDump::getBillNo)
                .last("LIMIT 1");  // 限制只返回1条记录

        List<Object> list = dumpService.listObjs(query);
        if (list != null && !list.isEmpty()) {
            // 如果已存在当天的流水号，则使用最新的一条记录作为基础
            billNo = (String) list.get(0);
        }

        // 5. 通过工具类生成下一个连续的流水号并返回
        return YouBianCodeUtil.getNextYouBianCode(billNo);
    }
}
