package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.ISapApiService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 示例不带参定时任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class SapBillDataJob implements Job {
	@Autowired
	private ISapApiService sapApiService;
	@Override
	public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
		//销售交货草稿同步
		sapApiService.getSalesdraft();
		//获取采购退货单草稿
		sapApiService.getPurreturnDraft();
		//获取销售退货单草稿
		sapApiService.getSalereturnDraft();
		//获取检验记录
		sapApiService.getInspection();
	}
}
