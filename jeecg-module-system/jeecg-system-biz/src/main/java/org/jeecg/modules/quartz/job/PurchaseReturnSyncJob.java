package org.jeecg.modules.quartz.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.service.ISapApiService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.PersistJobDataAfterExecution;


/**
 * 采购退货单同步定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class PurchaseReturnSyncJob implements Job {

    @Autowired
    private ISapApiService sapApiService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("开始同步采购退货单到SAP");
            sapApiService.syncPurreturn();
            log.info("采购退货单同步完成");
        } catch (Exception e) {
            log.error("采购退货单同步失败", e);
            throw new JobExecutionException(e);
        }
    }
}
