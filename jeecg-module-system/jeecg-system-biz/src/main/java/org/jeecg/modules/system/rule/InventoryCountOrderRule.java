package org.jeecg.modules.system.rule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.YouBianCodeUtil;
import org.jeecg.modules.admin.entity.WmsInventoryCountPlan;
import org.jeecg.modules.admin.entity.WmsStockCirculation;
import org.jeecg.modules.admin.service.IWmsInventoryCountPlanService;
import org.jeecg.modules.admin.service.IWmsStockCirculationService;
import org.jeecg.modules.system.util.LocalDateTimeUtils;

import java.util.List;

/**
 * 入库单（流水号）
 */
public class InventoryCountOrderRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        IWmsInventoryCountPlanService recordService = (IWmsInventoryCountPlanService) SpringContextUtils.getBean("wmsInventoryCountPlanServiceImpl");
        String prefix = "ICN"; //编号前缀默认为SCL 如果规则参数不为空，则取自定义前缀
        int digit = 4;  // 编号默认后缀为
        if (params != null) {
            Object prefixStr = params.get("prefix");
            if (prefixStr != null) prefix = prefixStr.toString();

            Object digitStr = params.get("digit");
            if (digitStr != null) digit = Integer.parseInt(digitStr.toString());
        }

        // 构建编号
        String timeRule = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.YYYYMMDD);
        String isbn = prefix + timeRule + String.format("%0" + digit + "d", 0);
        // 获取今天的日期
//        LocalDate today = LocalDate.now();
        LambdaQueryWrapper<WmsInventoryCountPlan> query = new LambdaQueryWrapper<>();
        query.select(WmsInventoryCountPlan::getBillNo)
                .likeRight(WmsInventoryCountPlan::getBillNo, prefix + timeRule) // 按前缀模糊查询
//                .between(SpotRecord::getCreateTime, today.atStartOfDay(), today.atTime(23, 59, 59)) // 查询当天的记录
                .orderByDesc(WmsInventoryCountPlan::getBillNo)
                .last("LIMIT 1");
        List<Object> list = recordService.listObjs(query);
        if (list != null && list.size() > 0) {
            isbn = (String) list.get(0);
        }


        return YouBianCodeUtil.getNextYouBianCode(isbn);
    }

}
