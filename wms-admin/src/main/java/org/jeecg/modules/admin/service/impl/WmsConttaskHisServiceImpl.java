package org.jeecg.modules.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsConttaskHis;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import org.jeecg.modules.admin.mapper.WmsConttaskHisMapper;
import org.jeecg.modules.admin.mapper.WmsSenddetailMapper;
import org.jeecg.modules.admin.service.IWmsConttaskHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 历史任务表
 * @Author: jeecg-boot
 * @Date:   2024-07-30
 * @Version: V1.0
 */
@Service
public class WmsConttaskHisServiceImpl extends ServiceImpl<WmsConttaskHisMapper, WmsConttaskHis> implements IWmsConttaskHisService {
    @Autowired
    private WmsSenddetailMapper wmsSenddetailMapper;
    @Override
    public IPage<WmsStockdetail> queryHisDetail(WmsStockdetail wmsStockdetail, Integer pageNo, Integer pageSize, HttpServletRequest req) {

        // 根据 obDtlId 查询历史任务
        QueryWrapper<WmsConttaskHis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("src_line_id", wmsStockdetail.getObDtlId());
        List<WmsConttaskHis> wmsConttaskHisList = this.list(queryWrapper);
        if (wmsConttaskHisList.isEmpty()) {
            return new Page<>(pageNo, pageSize); // 如果没有历史任务，直接返回空分页
        }

        // 查询出库单明细信息
        WmsSenddetail wmsSenddetail = wmsSenddetailMapper.selectById(wmsStockdetail.getObDtlId());

        // 创建分页对象并返回结果
        Page<WmsStockdetail> page = new Page<>(pageNo, pageSize);
        IPage<WmsStockdetail> pageList = new Page<>(pageNo, pageSize);

        // 如果有出库单明细信息
        if (wmsSenddetail != null) {
            // 遍历历史任务并构建库存明细对象
            List<WmsStockdetail> wmsStockdetailList = new ArrayList<>();
            wmsConttaskHisList.forEach(wmsConttaskHis -> {
                WmsStockdetail stockdetail = new WmsStockdetail();
                // 设置库存明细对象的属性
                stockdetail.setItemCode(wmsSenddetail.getItemNumber());
                stockdetail.setItemName(wmsSenddetail.getItemName());
                stockdetail.setItemSpec(wmsSenddetail.getItemSpec());
                stockdetail.setBatchCode(wmsConttaskHis.getBatchCode());
                stockdetail.setLocateCode(wmsConttaskHis.getSourcePos());
                stockdetail.setQuantity(wmsConttaskHis.getSplitPallet().equals("1")?wmsConttaskHis.getSplitCount() : wmsConttaskHis.getTaskPacket());
                stockdetail.setObDtlId(wmsStockdetail.getObDtlId());
                stockdetail.setTargetPosition(wmsStockdetail.getTargetPosition());
                stockdetail.setSplitCount(wmsConttaskHis.getSplitCount());
                stockdetail.setInvState(WmsConstant.InvStateEnum.ALLOCATED.getValue());
                // 将构建好的库存明细对象添加到列表
                wmsStockdetailList.add(stockdetail);
            });
            pageList.setRecords(wmsStockdetailList); // 设置分页记录
        }

        return pageList;
    }
}
