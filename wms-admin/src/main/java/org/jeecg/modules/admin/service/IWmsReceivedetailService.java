package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsReceivedetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 收货单据明细
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
public interface IWmsReceivedetailService extends IService<WmsReceivedetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsReceivedetail>
	 */
	public List<WmsReceivedetail> selectByMainId(String mainId);

    void forceEnd(List<WmsReceivedetail> wmsReceivedetails);

    void moveBack(List<WmsReceivedetail> wmsReceivedetails);
}
