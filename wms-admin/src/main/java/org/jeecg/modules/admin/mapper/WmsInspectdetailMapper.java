package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsInspectdetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 检验明细
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
public interface WmsInspectdetailMapper extends BaseMapper<WmsInspectdetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsInspectdetail>
   */
	public List<WmsInspectdetail> selectByMainId(@Param("mainId") String mainId);
}
