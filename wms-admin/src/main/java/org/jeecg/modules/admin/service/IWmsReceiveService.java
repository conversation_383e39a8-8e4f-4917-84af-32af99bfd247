package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsReceivedetail;
import org.jeecg.modules.admin.entity.WmsReceive;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.vo.WmsRecevieAndDetailPage;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 收货主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-14
 * @Version: V1.0
 */
public interface IWmsReceiveService extends IService<WmsReceive> {

	/**
	 * 添加一对多
	 *
	 * @param wmsReceive
	 * @param wmsReceivedetailList
	 */
	public void saveMain(WmsReceive wmsReceive,List<WmsReceivedetail> wmsReceivedetailList) ;

	/**
	 * 修改一对多
	 *
	 * @param wmsReceive
	 * @param wmsReceivedetailList
	 */
	public void updateMain(WmsReceive wmsReceive,List<WmsReceivedetail> wmsReceivedetailList);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	Map<String, Object> getWeekReceive(String startTime, String endTime);

	Map<String, Object> getDayReceive(String date);

    void purchaseReceive(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList);

    IPage<WmsReceive> queryReceiveByItem(Page<WmsReceive> page, String itemCode, String itemName, String batchCode, QueryWrapper<WmsReceive> queryWrapper);

    /**
     * 采购入库
     * @param wmsReceive 入库主单据
     * @param wmsReceivedetailList 入库明细列表
     */
    void purchaseInbound(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList);

    /**
     * 手动重新推送SAP采购送检单
     * @param wmsReceivedetailList 收货明细列表
     * @return 推送结果信息
     */
    public Map<Boolean, String> manualResendPurchaseInspection(List<WmsReceivedetail> wmsReceivedetailList);

    /**
     * 手动重新推送SAP采购送检单（增强版，支持设置送检人、送检日期和送检数量）
     * @param wmsReceivedetailList 收货明细列表
     * @param inspectionPerson 送检人
     * @param inspectionDate 送检日期
     * @param inspectionQty 送检数量
     * @return 推送结果信息
     */
    public Map<Boolean, String> manualResendPurchaseInspection(List<WmsReceivedetail> wmsReceivedetailList,
                                               String inspectionPerson,
                                               Date inspectionDate,
                                               Double inspectionQty);

	JSONObject getInspectionStatus();

	List<WmsRecevieAndDetailPage> getPendingInspectionList();
}
