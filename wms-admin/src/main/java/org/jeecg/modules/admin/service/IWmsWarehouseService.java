package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsWarehouse;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.entity.WmsZone;

import java.util.List;

/**
 * @Description: 仓库表
 * @Author: jeecg-boot
 * @Date:   2024-06-13
 * @Version: V1.0
 */
public interface IWmsWarehouseService extends IService<WmsWarehouse> {

    JSONArray queryWarehouseArea(String type);

    JSONArray queryWarehouseAreaPC();

    List<WmsZone> queryXBKWarehouseArea();
}
