package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 库存转储单
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Data
@TableName("wms_stock_dump")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_stock_dump对象", description="库存转储单")
public class WmsStockDump implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
	/**转储单据号*/
	@Excel(name = "转储单据号", width = 15)
    @ApiModelProperty(value = "转储单据号")
    private java.lang.String billNo;
    /**品号*/
    @Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private String itemCode;
    /**品名*/
    @Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private String itemName;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "stock_dump_bill_type")
	@Dict(dicCode = "stock_dump_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**转入仓库*/
	@Excel(name = "转入仓库", width = 15)
    @ApiModelProperty(value = "转入仓库")
    private java.lang.String fromWarehouse;
	/**转出仓库*/
	@Excel(name = "转出仓库", width = 15)
    @ApiModelProperty(value = "转出仓库")
    private java.lang.String toWarehouse;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**过账日期*/
	@Excel(name = "过账日期", width = 20, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过账日期")
    private java.util.Date postDate;
    /**批次数量*/
    @Excel(name = "批次数量", width = 15)
    @ApiModelProperty(value = "批次数量")
    private java.lang.Double batchQty;
	/**转储数量*/
	@Excel(name = "转储数量", width = 15)
    @ApiModelProperty(value = "转储数量")
    private java.lang.Double qty;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "approve_status")
	@Dict(dicCode = "approve_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
	@Dict(dicCode = "from_erp")
    @ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
	@Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
    /**账套信息*/
    @Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
    @ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
    /**行号*/
    @Excel(name = "行号", width = 15)
    @ApiModelProperty(value = "行号")
    private java.lang.Integer lineNo;
    /**库存id*/
    @Excel(name = "库存id", width = 15)
    @ApiModelProperty(value = "库存id")
    private java.lang.String stockId;
    /**待转储物料id*/
    @Excel(name = "待转储物料id", width = 15)
    @ApiModelProperty(value = "待转储物料id")
    private java.lang.String itemWaitDumpId;
    /**单据明细id*/
    @Excel(name = "单据明细id", width = 15)
    @ApiModelProperty(value = "单据明细id")
    private java.lang.String srcLineId;
    /**库存类型*/
    @Excel(name = "库存类型", width = 15, dicCode = "stock_type")
    @Dict(dicCode = "stock_type")
    @ApiModelProperty(value = "库存类型")
    private java.lang.String stockType;
}
