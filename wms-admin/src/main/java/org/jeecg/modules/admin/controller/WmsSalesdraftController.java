package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.admin.vo.WmsShippingPlanPage;
import org.jeecg.modules.admin.vo.WmsShippingPlanBPage;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsSalesdraftDetail;
import org.jeecg.modules.admin.entity.WmsSalesdraft;
import org.jeecg.modules.admin.vo.WmsSalesdraftPage;
import org.jeecg.modules.admin.service.IWmsSalesdraftService;
import org.jeecg.modules.admin.service.IWmsSalesdraftDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 销售交货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Api(tags="销售交货草稿")
@RestController
@RequestMapping("/admin/wmsSalesdraft")
@Slf4j
public class WmsSalesdraftController {
	@Autowired
	private IWmsSalesdraftService wmsSalesdraftService;
	@Autowired
	private IWmsSalesdraftDetailService wmsSalesdraftDetailService;

	 /**
	  * 销售交货草稿-分页列表查询或详情查询
	  *
	  * @param wmsSalesdraft 销售交货草稿实体
	  * @param pageNo        页码
	  * @param pageSize      每页条数
	  * @param itemNumber    物品编号（可选）
	  * @param itemName      物品名称（可选）
	  * @param req           HTTP 请求
	  * @return 分页结果或详情列表
	  */
	 @ApiOperation(value = "销售交货草稿-分页列表查询或详情查询", notes = "根据参数决定分页列表查询或详情查询")
	 @GetMapping("/list")
	 public Result<IPage<WmsSalesdraft>> queryPageList(
			 WmsSalesdraft wmsSalesdraft,
			 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			 @RequestParam(name = "itemNumber", required = false) String itemNumber,
			 @RequestParam(name = "itemName", required = false) String itemName,
			 HttpServletRequest req) {

		 // 初始化查询条件
		 QueryWrapper<WmsSalesdraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsSalesdraft, req.getParameterMap());
		 Page<WmsSalesdraft> page = new Page<>(pageNo, pageSize);

		 // 判断是否包含 itemNumber 或 itemName 参数
		 if (StringUtils.isNotBlank(itemNumber) || StringUtils.isNotBlank(itemName)) {
			 // 调用按物品查询的逻辑
			 IPage<WmsSalesdraft> detailPage = wmsSalesdraftService.querySalesdraftByItem(page, itemNumber, itemName, queryWrapper);
			 return Result.OK(detailPage);
		 } else {
			 // 调用原有的分页查询逻辑
			 IPage<WmsSalesdraft> pageList = wmsSalesdraftService.page(page, queryWrapper);
			 return Result.OK(pageList);
		 }
	 }

	/**
	 *   添加
	 *
	 * @param wmsSalesdraftPage
	 * @return
	 */
	@AutoLog(value = "销售交货草稿-添加")
	@ApiOperation(value="销售交货草稿-添加", notes="销售交货草稿-添加")
    @RequiresPermissions("admin:wms_salesdraft:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsSalesdraftPage wmsSalesdraftPage) {
		WmsSalesdraft wmsSalesdraft = new WmsSalesdraft();
		BeanUtils.copyProperties(wmsSalesdraftPage, wmsSalesdraft);
		wmsSalesdraftService.saveMain(wmsSalesdraft, wmsSalesdraftPage.getWmsSalesdraftDetailList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param wmsSalesdraftPage
	 * @return
	 */
	@AutoLog(value = "销售交货草稿-编辑")
	@ApiOperation(value="销售交货草稿-编辑", notes="销售交货草稿-编辑")
    @RequiresPermissions("admin:wms_salesdraft:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsSalesdraftPage wmsSalesdraftPage) {
		WmsSalesdraft wmsSalesdraft = new WmsSalesdraft();
		BeanUtils.copyProperties(wmsSalesdraftPage, wmsSalesdraft);
		WmsSalesdraft wmsSalesdraftEntity = wmsSalesdraftService.getById(wmsSalesdraft.getId());
		if(wmsSalesdraftEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsSalesdraftService.updateMain(wmsSalesdraft, wmsSalesdraftPage.getWmsSalesdraftDetailList());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "销售交货草稿-通过id删除")
	@ApiOperation(value="销售交货草稿-通过id删除", notes="销售交货草稿-通过id删除")
    @RequiresPermissions("admin:wms_salesdraft:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsSalesdraftService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "销售交货草稿-批量删除")
	@ApiOperation(value="销售交货草稿-批量删除", notes="销售交货草稿-批量删除")
    @RequiresPermissions("admin:wms_salesdraft:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsSalesdraftService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "销售交货草稿-通过id查询")
	@ApiOperation(value="销售交货草稿-通过id查询", notes="销售交货草稿-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsSalesdraft> queryById(@RequestParam(name="id",required=true) String id) {
		WmsSalesdraft wmsSalesdraft = wmsSalesdraftService.getById(id);
		if(wmsSalesdraft==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsSalesdraft);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "销售交货草稿明细-通过主表ID查询")
	@ApiOperation(value="销售交货草稿明细-通过主表ID查询", notes="销售交货草稿明细-通过主表ID查询")
	@GetMapping(value = "/queryWmsSalesdraftDetailByMainId")
	public Result<IPage<WmsSalesdraftDetail>> queryWmsSalesdraftDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsSalesdraftDetail> wmsSalesdraftDetailList = wmsSalesdraftDetailService.selectByMainId(id);
		IPage <WmsSalesdraftDetail> page = new Page<>();
		page.setRecords(wmsSalesdraftDetailList);
		page.setTotal(wmsSalesdraftDetailList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsSalesdraft
    */
    @RequiresPermissions("admin:wms_salesdraft:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsSalesdraft wmsSalesdraft) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsSalesdraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsSalesdraft, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<WmsSalesdraft>  wmsSalesdraftList = wmsSalesdraftService.list(queryWrapper);

      // Step.3 组装pageList
      List<WmsSalesdraftPage> pageList = new ArrayList<WmsSalesdraftPage>();
      for (WmsSalesdraft main : wmsSalesdraftList) {
          WmsSalesdraftPage vo = new WmsSalesdraftPage();
          BeanUtils.copyProperties(main, vo);
          List<WmsSalesdraftDetail> wmsSalesdraftDetailList = wmsSalesdraftDetailService.selectByMainId(main.getId());
          vo.setWmsSalesdraftDetailList(wmsSalesdraftDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "销售交货草稿列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsSalesdraftPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("销售交货草稿数据", "导出人:"+sysUser.getRealname(), "销售交货草稿"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

	 /**
	  * 将草稿数据生成正式数据
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "销售交货草稿-生成正式数据")
	 @ApiOperation(value="销售交货草稿-生成正式数据", notes="销售交货草稿-生成正式数据")
	 @GetMapping(value = "/generateFormalData")
	 public Result<String> generateFormalData(@RequestParam(name = "ids", required = true) String ids) {
		 wmsSalesdraftService.createFormalSalesData(ids);
		 return Result.OK("生成正式数据成功！");
	 }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_salesdraft:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsSalesdraftPage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsSalesdraftPage.class, params);
              for (WmsSalesdraftPage page : list) {
                  WmsSalesdraft po = new WmsSalesdraft();
                  BeanUtils.copyProperties(page, po);
                  wmsSalesdraftService.saveMain(po, page.getWmsSalesdraftDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

	 /**
	  * 发货计划状态统计
	  *
	  */
	 @ApiOperation(value="发货计划状态统计", notes="发货计划状态统计")
	 @GetMapping(value = "/getSendPlanStatus")
	 public Result<JSONObject> getSendPlanStatus() {
		 JSONObject result = wmsSalesdraftService.getSendPlanStatus();
		 return Result.OK(result);
	 }

	 /**
	  * 发货计划列表
	  */
	 @ApiOperation(value="发货计划列表", notes="发货计划列表")
	 @GetMapping(value = "/getSendPlanList")
	 public Result<List<WmsShippingPlanPage>> getSendPlanList() {
		 List<WmsShippingPlanPage> list = wmsSalesdraftService.getSendPlanList();
		 return Result.OK(list);
	 }

	 /**
	  * 发货计划列表B版本
	  */
	 @ApiOperation(value="发货计划列表B版本", notes="发货计划列表B版本")
	 @GetMapping(value = "/getSendPlanListForB")
	 public Result<List<WmsShippingPlanBPage>> getSendPlanListForB() {
		 List<WmsShippingPlanBPage> list = wmsSalesdraftService.getSendPlanListForB();
		 return Result.OK(list);
	 }

 }
