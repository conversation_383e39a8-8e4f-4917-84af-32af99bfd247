package org.jeecg.modules.admin.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface WmsStockdetailMapper extends BaseMapper<WmsStockdetail> {

    IPage<WmsStockdetail> listSummary(Page<WmsStockdetail> page, @Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper);

    List<WmsStockdetail> queryWithNullObDtlId(@Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper,@Param("levelNo") String levelNo);

    List<WmsStockdetail> queryWithNullObDtlIdForFlat(@Param(Constants.WRAPPER) QueryWrapper<WmsStockdetail> queryWrapper);

    List<WmsStockdetail> queryWithNullObDtlIdForLine(@Param(Constants.WRAPPER)QueryWrapper<WmsStockdetail> queryWrapper);
}
