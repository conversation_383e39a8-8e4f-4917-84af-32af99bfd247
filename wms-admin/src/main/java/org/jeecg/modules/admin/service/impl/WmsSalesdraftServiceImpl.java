package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsSalesdraft;
import org.jeecg.modules.admin.entity.WmsSalesdraftDetail;
import org.jeecg.modules.admin.entity.WmsSend;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.mapper.WmsSalesdraftDetailMapper;
import org.jeecg.modules.admin.mapper.WmsSalesdraftMapper;
import org.jeecg.modules.admin.mapper.WmsSenddetailMapper;
import org.jeecg.modules.admin.service.IWmsSalesdraftService;
import org.jeecg.modules.admin.service.IWmsSendService;
import org.jeecg.modules.admin.vo.WmsShippingPlanBPage;
import org.jeecg.modules.admin.vo.WmsShippingPlanPage;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import org.jeecg.modules.admin.util.DateUtils;

/**
 * @Description: 销售交货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Service
public class WmsSalesdraftServiceImpl extends ServiceImpl<WmsSalesdraftMapper, WmsSalesdraft> implements IWmsSalesdraftService {

	@Autowired
	private WmsSalesdraftMapper wmsSalesdraftMapper;
	@Autowired
	private WmsSalesdraftDetailMapper wmsSalesdraftDetailMapper;
	@Autowired
	private IWmsSendService wmsSendService;
	@Autowired
	private WmsSenddetailMapper wmsSenddetailMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsSalesdraft wmsSalesdraft, List<WmsSalesdraftDetail> wmsSalesdraftDetailList) {
		wmsSalesdraftMapper.insert(wmsSalesdraft);
		if(wmsSalesdraftDetailList!=null && wmsSalesdraftDetailList.size()>0) {
			for(WmsSalesdraftDetail entity:wmsSalesdraftDetailList) {
				//外键设置
				entity.setWorkId(wmsSalesdraft.getId());
				wmsSalesdraftDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsSalesdraft wmsSalesdraft,List<WmsSalesdraftDetail> wmsSalesdraftDetailList) {
		wmsSalesdraftMapper.updateById(wmsSalesdraft);

		//1.先删除子表数据
		wmsSalesdraftDetailMapper.deleteByMainId(wmsSalesdraft.getId());

		//2.子表数据重新插入
		if(wmsSalesdraftDetailList!=null && wmsSalesdraftDetailList.size()>0) {
			for(WmsSalesdraftDetail entity:wmsSalesdraftDetailList) {
				//外键设置
				entity.setWorkId(wmsSalesdraft.getId());
				wmsSalesdraftDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsSalesdraftDetailMapper.deleteByMainId(id);
		wmsSalesdraftMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsSalesdraftDetailMapper.deleteByMainId(id.toString());
			wmsSalesdraftMapper.deleteById(id);
		}
	}

	@Override
	public void createFormalSalesData(String ids) {
		if (ids == null || ids.isEmpty()) {
			return;
		}

		Arrays.stream(ids.split(","))
				.map(wmsSalesdraftMapper::selectById)
				.filter(Objects::nonNull)
				.forEach(this::handleSalesData);
	}

	private void handleSalesData(WmsSalesdraft salesDraft) {
		WmsSend send = buildSend(salesDraft);
		List<WmsSenddetail> sendDetails = buildSendDetails(salesDraft.getId(), send);

		wmsSendService.saveMain(send, sendDetails);

		// 更新草稿数据标记为正式数据
		salesDraft.setFormalDataFlag(WmsConstant.FormalDataFlagEnum.YES.getValue());
		wmsSalesdraftMapper.updateById(salesDraft);
	}

	private WmsSend buildSend(WmsSalesdraft salesDraft) {
		String code = (String) FillRuleUtil.executeRule(FillRuleConstant.WMS_SEND_CODE_RULE, new JSONObject());
		WmsSend send = new WmsSend();
		send.setId(salesDraft.getId());
		send.setSerialNumber(salesDraft.getSerialNumber());
		send.setWorkNo(salesDraft.getWorkNo());
		send.setBillNo(code);
		send.setBillName(salesDraft.getBillName());
		send.setBillStatus("1");  // 默认状态
		send.setBillType("XSCK");
		send.setFromErp(salesDraft.getFromErp());
		send.setErpSync(WmsConstant.ErpSyncEnum.INIT.getValue());
		send.setRemark(salesDraft.getRemark());
		send.setRemark2(salesDraft.getRemark2());
		send.setAccountCode(salesDraft.getAccountCode());
		send.setPostDate(salesDraft.getPostDate());
		send.setDeliveryDate(salesDraft.getDeliveryDate());
		send.setCustomerCode(salesDraft.getCustomerCode());
		send.setCustomerName(salesDraft.getCustomerName());
		send.setDeliveryType(salesDraft.getDeliveryType());
		send.setCargoNo(salesDraft.getCargoNo());
		return send;
	}

	private List<WmsSenddetail> buildSendDetails(String draftId, WmsSend send) {
		List<WmsSalesdraftDetail> draftDetails = wmsSalesdraftDetailMapper.selectByMainId(draftId);
		if (draftDetails == null || draftDetails.isEmpty()) {
			return Collections.emptyList();
		}

		return draftDetails.stream().map(draftDetail -> {
			WmsSenddetail sendDetail = new WmsSenddetail();
			sendDetail.setBillNo(send.getBillNo());
			sendDetail.setBillId(send.getId());
			sendDetail.setItemNumber(draftDetail.getItemNumber());
			sendDetail.setItemName(draftDetail.getItemName());
			sendDetail.setBatchCode(draftDetail.getBatchCode());
			sendDetail.setItemSpec(draftDetail.getItemSpec());

			// 根据是否有 batchCode 来决定使用哪个数量
			Double planQty;
			if (draftDetail.getBatchCode() != null && !draftDetail.getBatchCode().isEmpty()) {
				// 如果有 batchCode，则使用 PlanQty
				planQty = Optional.ofNullable(draftDetail.getPlanQty()).orElse(0.0);
			} else {
				// 如果没有 batchCode，则使用 AllQty
				planQty = Optional.ofNullable(draftDetail.getAllQty()).orElse(0.0);
			}
			sendDetail.setPlanQty(planQty);

			sendDetail.setDisQty(0.0); // 初始分配数量
			sendDetail.setActQty(0.0); // 初始实际出库数量
			sendDetail.setLineState("1"); // 默认状态
			sendDetail.setRemark(draftDetail.getRemark());
			sendDetail.setWarehouseCode(draftDetail.getWarehouseCode());
			sendDetail.setWarehouseName(draftDetail.getWarehouseName());
			sendDetail.setLineNo(draftDetail.getLineNo());

			return sendDetail;
		}).collect(Collectors.toList());
	}

	@Override
	public IPage<WmsSalesdraft> querySalesdraftByItem(Page<WmsSalesdraft> page, String itemNumber, String itemName, QueryWrapper<WmsSalesdraft> queryWrapper) {
		// 检查是否需要根据 itemNumber 或 itemName 进行过滤
		if (StringUtils.isNotBlank(itemNumber) || StringUtils.isNotBlank(itemName)) {
			// 构建子表的查询条件
			QueryWrapper<WmsSalesdraftDetail> detailQueryWrapper = new QueryWrapper<>();
			if (StringUtils.isNotBlank(itemNumber)) {
				detailQueryWrapper.eq("item_number", itemNumber);
			}
			if (StringUtils.isNotBlank(itemName)) {
				detailQueryWrapper.like("item_name", itemName);
			}

			// 查询符合条件的 salesdraftId
			List<String> salesdraftIds = wmsSalesdraftDetailMapper.selectList(detailQueryWrapper)
					.stream()
					.map(WmsSalesdraftDetail::getWorkId)
					.distinct()
					.collect(Collectors.toList());

			if (salesdraftIds.isEmpty()) {
				// 如果没有匹配的 salesdraftId，则返回空的分页结果
				return new Page<>();
			}

			// 将 salesdraftIds 作为主表的过滤条件
			queryWrapper.in("id", salesdraftIds);
		}

		// 执行主表的分页查询
		return this.page(page, queryWrapper);
	}

	@Override
	public JSONObject getSendPlanStatus() {
		// 创建返回结果对象
		JSONObject result = new JSONObject();

		// 获取当前日期和上周日期
		Date currentDate = new Date();
		Date lastWeekDate = DateUtils.addDays(currentDate, -7);
		Date twoWeeksAgoDate = DateUtils.addDays(currentDate, -14);

		// 批量查询最近两周的销售交货草稿记录
		QueryWrapper<WmsSalesdraft> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
		queryWrapper.ge("create_time", twoWeeksAgoDate);
		List<WmsSalesdraft> allSalesDraftList = this.list(queryWrapper);

		// 分离当前周和上周的数据
		List<WmsSalesdraft> currentWeekList = new ArrayList<>();
		List<WmsSalesdraft> lastWeekList = new ArrayList<>();

		for (WmsSalesdraft draft : allSalesDraftList) {
			if (draft.getCreateTime().compareTo(lastWeekDate) >= 0) {
				currentWeekList.add(draft);
			} else {
				lastWeekList.add(draft);
			}
		}

		// 提取所有销售草稿ID，批量查询详情
		Set<String> allDraftIds = allSalesDraftList.stream()
				.map(WmsSalesdraft::getId)
				.collect(Collectors.toSet());

		// 批量查询所有销售草稿详情
		Map<String, List<WmsSalesdraftDetail>> draftDetailMap = new HashMap<>();
		if (!allDraftIds.isEmpty()) {
			QueryWrapper<WmsSalesdraftDetail> detailQueryWrapper = new QueryWrapper<>();
			detailQueryWrapper.in("work_id", allDraftIds); // 使用workId字段关联主表ID
			List<WmsSalesdraftDetail> allDetails = wmsSalesdraftDetailMapper.selectList(detailQueryWrapper);

			// 按主表ID分组
			draftDetailMap = allDetails.stream()
					.collect(Collectors.groupingBy(WmsSalesdraftDetail::getWorkId));
		}

		// 提取所有序列号，批量查询WmsSend
		Set<Integer> allSerialNumbers = allSalesDraftList.stream()
				.filter(draft -> "1".equals(draft.getFormalDataFlag()))
				.map(WmsSalesdraft::getSerialNumber)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		Map<Integer, WmsSend> sendMap = new HashMap<>();
		if (!allSerialNumbers.isEmpty()) {
			QueryWrapper<WmsSend> sendQueryWrapper = new QueryWrapper<>();
			sendQueryWrapper.in("serial_number", allSerialNumbers);
			List<WmsSend> sendList = wmsSendService.list(sendQueryWrapper);

			sendMap = sendList.stream()
					.filter(send -> send.getSerialNumber() != null)
					.collect(Collectors.toMap(WmsSend::getSerialNumber, send -> send));
		}

		// 提取所有WmsSend的ID，批量查询WmsSenddetail
		Set<String> allSendIds = sendMap.values().stream()
				.map(WmsSend::getId)
				.collect(Collectors.toSet());

		Map<String, List<WmsSenddetail>> sendDetailMap = new HashMap<>();
		if (!allSendIds.isEmpty()) {
			QueryWrapper<WmsSenddetail> sendDetailQueryWrapper = new QueryWrapper<>();
			sendDetailQueryWrapper.in("bill_id", allSendIds);
			List<WmsSenddetail> allSendDetails = wmsSenddetailMapper.selectList(sendDetailQueryWrapper);

			// 按账单ID分组
			sendDetailMap = allSendDetails.stream()
					.collect(Collectors.groupingBy(WmsSenddetail::getBillId));
		}

		// 统计当前周数据
		StatusCount currentWeekCount = calculateStatusCount(currentWeekList, draftDetailMap, sendMap, sendDetailMap);

		// 统计上周数据
		StatusCount lastWeekCount = calculateStatusCount(lastWeekList, draftDetailMap, sendMap, sendDetailMap);

		// 计算完成率
		int totalOrders = currentWeekCount.getTotalCount();
		double completionRate = totalOrders > 0 ? (double) currentWeekCount.waitingLoadCount / totalOrders * 100 : 0;

		int lastWeekTotalOrders = lastWeekCount.getTotalCount();
		double lastWeekCompletionRate = lastWeekTotalOrders > 0 ? (double) lastWeekCount.waitingLoadCount / lastWeekTotalOrders * 100 : 0;

		// 计算趋势
		JSONObject waitingPrepareTrend = calculateTrend(currentWeekCount.waitingPrepareCount, lastWeekCount.waitingPrepareCount);
		JSONObject preparingTrend = calculateTrend(currentWeekCount.preparingCount, lastWeekCount.preparingCount);
		JSONObject waitingLoadTrend = calculateTrend(currentWeekCount.waitingLoadCount, lastWeekCount.waitingLoadCount);
		JSONObject exceptionOrdersTrend = calculateTrend(currentWeekCount.exceptionOrdersCount, lastWeekCount.exceptionOrdersCount);
		JSONObject completionRateTrend = calculateTrend(completionRate, lastWeekCompletionRate);

		// 构建返回结果
		result.put("waitingPrepareCount", currentWeekCount.waitingPrepareCount);
		result.put("preparingCount", currentWeekCount.preparingCount);
		result.put("waitingLoadCount", currentWeekCount.waitingLoadCount);
		result.put("exceptionOrdersCount", currentWeekCount.exceptionOrdersCount);
		result.put("completionRate", Math.round(completionRate * 10) / 10.0);

		result.put("waitingPrepareTrend", waitingPrepareTrend);
		result.put("preparingTrend", preparingTrend);
		result.put("waitingLoadTrend", waitingLoadTrend);
		result.put("exceptionOrdersTrend", exceptionOrdersTrend);
		result.put("completionRateTrend", completionRateTrend);

		return result;
	}

	/**
	 * 计算状态统计
	 */
	private StatusCount calculateStatusCount(List<WmsSalesdraft> salesDraftList,
											 Map<String, List<WmsSalesdraftDetail>> draftDetailMap,
											 Map<Integer, WmsSend> sendMap,
											 Map<String, List<WmsSenddetail>> sendDetailMap) {

		StatusCount count = new StatusCount();

		for (WmsSalesdraft salesDraft : salesDraftList) {
			List<WmsSalesdraftDetail> detailList = draftDetailMap.getOrDefault(salesDraft.getId(), Collections.emptyList());

			// 条件1：如果主表的正式单据为0，那么所有子表就是待备货
			if ("0".equals(salesDraft.getFormalDataFlag())) {
				count.waitingPrepareCount += detailList.size();
				continue;
			}

			// 如果主表的正式单据状态为1，需要进一步查询WmsSend表
			if ("1".equals(salesDraft.getFormalDataFlag()) && salesDraft.getSerialNumber() != null) {
				WmsSend wmsSend = sendMap.get(salesDraft.getSerialNumber());

				if (wmsSend != null) {
					List<WmsSenddetail> sendDetailList = sendDetailMap.getOrDefault(wmsSend.getId(), Collections.emptyList());

					// 条件2：billStatus为1，erpSync为0，lineState为1，统计为备货中
					if ("1".equals(wmsSend.getBillStatus()) && "0".equals(wmsSend.getErpSync())) {
						count.preparingCount += (int) sendDetailList.stream()
								.filter(detail -> "1".equals(detail.getLineState()))
								.count();
					}

					// 条件3：billStatus为3，erpSync为2，lineState为4，统计为待装车
					if ("3".equals(wmsSend.getBillStatus()) && "2".equals(wmsSend.getErpSync())) {
						count.waitingLoadCount += (int) sendDetailList.stream()
								.filter(detail -> "4".equals(detail.getLineState()))
								.count();
					}

					// 条件4：billStatus为3，erpSync为3，lineState为4，统计为异常订单
					if ("3".equals(wmsSend.getBillStatus()) && "3".equals(wmsSend.getErpSync())) {
						count.exceptionOrdersCount += (int) sendDetailList.stream()
								.filter(detail -> "4".equals(detail.getLineState()))
								.count();
					}
				}
			}
		}

		return count;
	}

	/**
	 * 状态统计内部类
	 */
	private static class StatusCount {
		int waitingPrepareCount = 0;
		int preparingCount = 0;
		int waitingLoadCount = 0;
		int exceptionOrdersCount = 0;

		int getTotalCount() {
			return waitingPrepareCount + preparingCount + waitingLoadCount + exceptionOrdersCount;
		}
	}

	/**
	 * 计算趋势
	 * @param currentValue 当前值
	 * @param lastWeekValue 上周值
	 * @return 趋势对象，包含类型和变化百分比
	 */
	private JSONObject calculateTrend(double currentValue, double lastWeekValue) {
		JSONObject trend = new JSONObject();

		if (lastWeekValue == 0) {
			// 如果上周值为0，则无法计算百分比变化，视为增加
			trend.put("type", "increase");
			trend.put("value", 100.0); // 增加100%
		} else {
			double changePercent = (currentValue - lastWeekValue) / lastWeekValue * 100;

			if (changePercent > 0) {
				trend.put("type", "increase");
			} else if (changePercent < 0) {
				trend.put("type", "decrease");
				changePercent = Math.abs(changePercent); // 转为正值
			} else {
				trend.put("type", "stable");
			}

			trend.put("value", Math.round(changePercent * 10) / 10.0); // 保留一位小数
		}

		return trend;
	}

	@Override
	public List<WmsShippingPlanPage> getSendPlanList() {
		// 创建返回结果列表
		List<WmsShippingPlanPage> resultList = new ArrayList<>();

		// 获取当前日期和上周日期
		Date currentDate = new Date();
		Date lastWeekDate = DateUtils.addDays(currentDate, -7);

		// 批量查询最近一周的销售交货草稿记录
		QueryWrapper<WmsSalesdraft> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
		queryWrapper.ge("create_time", lastWeekDate);
		List<WmsSalesdraft> salesDraftList = this.list(queryWrapper);

		// 如果没有数据，直接返回空列表
		if (salesDraftList.isEmpty()) {
			return resultList;
		}

		// 提取所有销售草稿ID，批量查询详情
		Set<String> allDraftIds = salesDraftList.stream()
				.map(WmsSalesdraft::getId)
				.collect(Collectors.toSet());

		// 批量查询所有销售草稿详情
		Map<String, List<WmsSalesdraftDetail>> draftDetailMap = new HashMap<>();
		if (!allDraftIds.isEmpty()) {
			QueryWrapper<WmsSalesdraftDetail> detailQueryWrapper = new QueryWrapper<>();
			detailQueryWrapper.in("work_id", allDraftIds); // 使用workId字段关联主表ID
			List<WmsSalesdraftDetail> allDetails = wmsSalesdraftDetailMapper.selectList(detailQueryWrapper);

			// 按主表ID分组
			draftDetailMap = allDetails.stream()
					.collect(Collectors.groupingBy(WmsSalesdraftDetail::getWorkId));
		}

		// 提取所有序列号，批量查询WmsSend
		Set<Integer> allSerialNumbers = salesDraftList.stream()
				.filter(draft -> "1".equals(draft.getFormalDataFlag()))
				.map(WmsSalesdraft::getSerialNumber)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		Map<Integer, WmsSend> sendMap = new HashMap<>();
		if (!allSerialNumbers.isEmpty()) {
			QueryWrapper<WmsSend> sendQueryWrapper = new QueryWrapper<>();
			sendQueryWrapper.in("serial_number", allSerialNumbers);
			List<WmsSend> sendList = wmsSendService.list(sendQueryWrapper);

			sendMap = sendList.stream()
					.filter(send -> send.getSerialNumber() != null)
					.collect(Collectors.toMap(WmsSend::getSerialNumber, send -> send));
		}

		// 提取所有WmsSend的ID，批量查询WmsSenddetail
		Set<String> allSendIds = sendMap.values().stream()
				.map(WmsSend::getId)
				.collect(Collectors.toSet());

		Map<String, List<WmsSenddetail>> sendDetailMap = new HashMap<>();
		if (!allSendIds.isEmpty()) {
			QueryWrapper<WmsSenddetail> sendDetailQueryWrapper = new QueryWrapper<>();
			sendDetailQueryWrapper.in("bill_id", allSendIds);
			List<WmsSenddetail> allSendDetails = wmsSenddetailMapper.selectList(sendDetailQueryWrapper);

			// 按账单ID分组
			sendDetailMap = allSendDetails.stream()
					.collect(Collectors.groupingBy(WmsSenddetail::getBillId));
		}

		// 构建发货计划列表
		for (WmsSalesdraft salesDraft : salesDraftList) {
			List<WmsSalesdraftDetail> detailList = draftDetailMap.getOrDefault(salesDraft.getId(), Collections.emptyList());

			for (WmsSalesdraftDetail detail : detailList) {
				WmsShippingPlanPage planPage = new WmsShippingPlanPage();

				// 设置基本信息
				planPage.setWorkNo(salesDraft.getWorkNo());
				planPage.setCargoNo(salesDraft.getCargoNo());
				planPage.setCustomerName(salesDraft.getCustomerName());
				planPage.setItemNumber(detail.getItemNumber());
				planPage.setItemName(detail.getItemName());
				planPage.setItemUnit(detail.getItemUnit());
				planPage.setBatchCode(detail.getBatchCode());
				planPage.setQuantity(detail.getPlanQty());
				planPage.setRemark(salesDraft.getRemark());

				// 设置状态
				String status = determineStatus(salesDraft, detail, sendMap, sendDetailMap);
				planPage.setStatus(status);

				resultList.add(planPage);
			}
		}

		return resultList;
	}

	/**
	 * 确定发货计划的状态
	 */
	private String determineStatus(WmsSalesdraft salesDraft,
								WmsSalesdraftDetail detail,
								Map<Integer, WmsSend> sendMap,
								Map<String, List<WmsSenddetail>> sendDetailMap) {

		// 条件1：如果主表的正式单据为0，那么所有子表就是待备货
		if ("0".equals(salesDraft.getFormalDataFlag())) {
			return "waiting_prepare";
		}

		// 如果主表的正式单据状态为1，需要进一步查询WmsSend表
		if ("1".equals(salesDraft.getFormalDataFlag()) && salesDraft.getSerialNumber() != null) {
			WmsSend wmsSend = sendMap.get(salesDraft.getSerialNumber());

			if (wmsSend != null) {
				List<WmsSenddetail> sendDetailList = sendDetailMap.getOrDefault(wmsSend.getId(), Collections.emptyList());

				// 找到对应的sendDetail
				Optional<WmsSenddetail> matchingSendDetail = sendDetailList.stream()
						.filter(sendDetail ->
							sendDetail.getItemNumber().equals(detail.getItemNumber()) &&
							((detail.getBatchCode() == null && sendDetail.getBatchCode() == null) ||
							(detail.getBatchCode() != null && detail.getBatchCode().equals(sendDetail.getBatchCode()))))
						.findFirst();

				if (matchingSendDetail.isPresent()) {
					WmsSenddetail sendDetail = matchingSendDetail.get();

					// 条件2：billStatus为1，erpSync为0，lineState为1，统计为备货中
					if ("1".equals(wmsSend.getBillStatus()) && "0".equals(wmsSend.getErpSync()) &&
						"1".equals(sendDetail.getLineState())) {
						return "preparing";
					}

					// 条件3：billStatus为3，erpSync为2，lineState为4，统计为待装车
					if ("3".equals(wmsSend.getBillStatus()) && "2".equals(wmsSend.getErpSync()) &&
						"4".equals(sendDetail.getLineState())) {
						return "waiting_load";
					}

					// 条件4：billStatus为3，erpSync为3，lineState为4，统计为异常订单
					if ("3".equals(wmsSend.getBillStatus()) && "3".equals(wmsSend.getErpSync()) &&
						"4".equals(sendDetail.getLineState())) {
						return "exception";
					}
				}
			}
		}

		// 默认为待备货
		return "waiting_prepare";
	}

	@Override
	public List<WmsShippingPlanBPage> getSendPlanListForB() {
		// 创建返回结果列表
		List<WmsShippingPlanBPage> resultList = new ArrayList<>();

		// 获取当前日期和上周日期
		Date currentDate = new Date();
		Date lastWeekDate = DateUtils.addDays(currentDate, -7);

		// 批量查询最近一周的销售交货草稿记录
		QueryWrapper<WmsSalesdraft> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
		queryWrapper.ge("create_time", lastWeekDate);
		List<WmsSalesdraft> salesDraftList = this.list(queryWrapper);

		// 如果没有数据，直接返回空列表
		if (salesDraftList.isEmpty()) {
			return resultList;
		}

		// 提取所有销售草稿ID，批量查询详情
		Set<String> allDraftIds = salesDraftList.stream()
				.map(WmsSalesdraft::getId)
				.collect(Collectors.toSet());

		// 批量查询所有销售草稿详情
		Map<String, List<WmsSalesdraftDetail>> draftDetailMap = new HashMap<>();
		if (!allDraftIds.isEmpty()) {
			QueryWrapper<WmsSalesdraftDetail> detailQueryWrapper = new QueryWrapper<>();
			detailQueryWrapper.in("work_id", allDraftIds); // 使用workId字段关联主表ID
			List<WmsSalesdraftDetail> allDetails = wmsSalesdraftDetailMapper.selectList(detailQueryWrapper);

			// 按主表ID分组
			draftDetailMap = allDetails.stream()
					.collect(Collectors.groupingBy(WmsSalesdraftDetail::getWorkId));
		}

		// 提取所有序列号，批量查询WmsSend
		Set<Integer> allSerialNumbers = salesDraftList.stream()
				.filter(draft -> "1".equals(draft.getFormalDataFlag()))
				.map(WmsSalesdraft::getSerialNumber)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		Map<Integer, WmsSend> sendMap = new HashMap<>();
		if (!allSerialNumbers.isEmpty()) {
			QueryWrapper<WmsSend> sendQueryWrapper = new QueryWrapper<>();
			sendQueryWrapper.in("serial_number", allSerialNumbers);
			List<WmsSend> sendList = wmsSendService.list(sendQueryWrapper);

			sendMap = sendList.stream()
					.filter(send -> send.getSerialNumber() != null)
					.collect(Collectors.toMap(WmsSend::getSerialNumber, send -> send));
		}

		// 提取所有WmsSend的ID，批量查询WmsSenddetail
		Set<String> allSendIds = sendMap.values().stream()
				.map(WmsSend::getId)
				.collect(Collectors.toSet());

		Map<String, List<WmsSenddetail>> sendDetailMap = new HashMap<>();
		if (!allSendIds.isEmpty()) {
			QueryWrapper<WmsSenddetail> sendDetailQueryWrapper = new QueryWrapper<>();
			sendDetailQueryWrapper.in("bill_id", allSendIds);
			List<WmsSenddetail> allSendDetails = wmsSenddetailMapper.selectList(sendDetailQueryWrapper);

			// 按账单ID分组
			sendDetailMap = allSendDetails.stream()
					.collect(Collectors.groupingBy(WmsSenddetail::getBillId));
		}

		// 构建发货计划列表
		for (WmsSalesdraft salesDraft : salesDraftList) {
			List<WmsSalesdraftDetail> detailList = draftDetailMap.getOrDefault(salesDraft.getId(), Collections.emptyList());

			for (WmsSalesdraftDetail detail : detailList) {
				WmsShippingPlanBPage planPage = new WmsShippingPlanBPage();

				// 设置基本信息
				planPage.setCargoNo(salesDraft.getCargoNo());
				planPage.setCustomerName(salesDraft.getCustomerName());
				planPage.setItemNumber(detail.getItemNumber());
				planPage.setItemName(detail.getItemName());
				planPage.setBatchCode(detail.getBatchCode());
				planPage.setQuantity(detail.getPlanQty());
				planPage.setUnitWeight(detail.getUnitWeight());
				planPage.setTotalWeight(detail.getTotalWeight());
				planPage.setRemark(salesDraft.getRemark2()); // 使用 remark2

				// 设置状态
				String status = determineStatus(salesDraft, detail, sendMap, sendDetailMap);
				planPage.setStatus(status);

				resultList.add(planPage);
			}
		}

		return resultList;
	}
}
