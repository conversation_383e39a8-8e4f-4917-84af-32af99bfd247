<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.admin.mapper.WmsStockdetailMapper">

    <select id="listSummary" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (SELECT materiel_type,
        item_code,
        item_name,
        item_spec,
        item_unit,
        batch_code,
        is_forbid,
        inv_state,
        erp_posting,
        zone_code,
        warehouse_code,
        account_code,
        MAX(create_time) AS create_time,  <!-- 使用 MAX 聚合函数处理 create_time -->
        SUM(quantity) AS quantity
        FROM wms_stockdetail
        GROUP BY materiel_type,
        item_code,
        item_name,
        item_spec,
        item_unit,
        batch_code,
        is_forbid,
        inv_state,
        zone_code,
        warehouse_code,
        account_code,
        erp_posting) n
        ${ew.customSqlSegment};
    </select>
    <select id="queryWithNullObDtlId" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (
        SELECT
            a.*
        FROM
            wms_stockdetail a
                LEFT JOIN wms_locate b ON a.locate_code = b.locate_code
        WHERE
            1 = 1
          AND a.erp_lock = '0'
          AND a.is_forbid = '0'
          AND b.locate_state = '1'
          AND IFNULL( a.ob_dtl_id, '' ) = ''
          AND IFNULL( a.target_position, '' ) = ''
          AND b.locate_operate_state IN ( '0', '1' )
          AND a.inv_state = '1'
        <if test="levelNo != null and levelNo != ''">
            AND b.level_no = #{levelNo}
        </if>
        ORDER BY
            b.col_no,b.distribution_depth ASC) n
            ${ew.customSqlSegment};
    </select>
    <select id="queryWithNullObDtlIdForFlat" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (
        SELECT
            a.*
        FROM
            wms_stockdetail a
        WHERE
            1 = 1
          AND a.erp_lock = '0'
          AND a.is_forbid = '0'
          AND IFNULL( a.ob_dtl_id, '' ) = ''
          AND IFNULL( a.target_position, '' ) = ''
          AND a.inv_state = '1') n
            ${ew.customSqlSegment};
    </select>
    <select id="queryWithNullObDtlIdForLine" resultType="org.jeecg.modules.admin.entity.WmsStockdetail">
        SELECT *
        FROM (
                 SELECT
                     a.*
                 FROM
                     wms_stockdetail a
                 WHERE
                     1 = 1
                   AND a.erp_lock = '0'
                   AND a.is_forbid = '0'
                   AND IFNULL(a.ob_dtl_id, '') = ''
                   AND IFNULL(a.target_position, '') = ''
                   AND a.inv_state = '1'
                   AND a.zone_code IN (
                     SELECT
                         z.zone_code
                     FROM
                         wms_zone z
                     WHERE
                         z.warehouse_code = (
                             SELECT
                                 w.warehouse_code
                             FROM
                                 wms_warehouse w
                             WHERE
                                 w.warehouse_type = 'XBK'
                         )
                 )) n
            ${ew.customSqlSegment};
    </select>
</mapper>