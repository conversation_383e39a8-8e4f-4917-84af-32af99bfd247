package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jeecg.modules.admin.entity.WmsDevice;
import org.jeecg.modules.admin.mapper.WmsDeviceMapper;
import org.jeecg.modules.admin.service.IWmsDeviceService;
import org.jeecg.modules.admin.util.HttpClientUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2024-06-13
 * @Version: V1.0
 */
@Service
public class WmsDeviceServiceImpl extends ServiceImpl<WmsDeviceMapper, WmsDevice> implements IWmsDeviceService {
    @Value("${wcs.api.base-url}")
    private String baseUrl;
    @Override
    public JSONObject changeDevice(String id) {
        String url = baseUrl+"/fromWms/changeRgv";
        Map<String, Integer> requestBody = new HashMap<>();
        requestBody.put("rgvNo", Integer.valueOf(id));
        JSONObject response = HttpClientUtil.sendPostRequest(url, requestBody);

        JSONObject result = new JSONObject();

        if (response == null) {
            result.put("result", false);
            result.put("message", "Request failed");
        } else if (response.getInteger("returnStatus") != null) {
            if (response.getInteger("returnStatus") == 0) {
                result.put("result", true);
                result.put("message", "更换设备成功");
            } else {
                result.put("result", false);
                result.put("message", "更换设备失败，原因:" + response.getString("returnInfo"));
            }
        } else {
            result.put("result", false);
            result.put("message", "更换设备失败，原因:未知错误");
        }

        return result;
    }
}
