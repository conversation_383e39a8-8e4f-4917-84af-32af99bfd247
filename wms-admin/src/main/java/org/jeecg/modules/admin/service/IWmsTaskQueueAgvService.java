package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsTaskQueueAgv;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
public interface IWmsTaskQueueAgvService extends IService<WmsTaskQueueAgv> {

    IPage<WmsTaskQueueAgv> pageList(Page<WmsTaskQueueAgv> page, QueryWrapper<WmsTaskQueueAgv> queryWrapper);
}
