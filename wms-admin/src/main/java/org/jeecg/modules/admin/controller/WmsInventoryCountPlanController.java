package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.dto.WmsInventoryCountPlanDTO;
import org.jeecg.modules.admin.entity.WmsInventoryCountPlan;
import org.jeecg.modules.admin.service.IWmsInventoryCountPlanService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 盘点方案
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Api(tags="盘点方案")
@RestController
@RequestMapping("/admin/wmsInventoryCountPlan")
@Slf4j
public class WmsInventoryCountPlanController extends JeecgController<WmsInventoryCountPlan, IWmsInventoryCountPlanService> {
	@Autowired
	private IWmsInventoryCountPlanService wmsInventoryCountPlanService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsInventoryCountPlan
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "盘点方案-分页列表查询")
	@ApiOperation(value="盘点方案-分页列表查询", notes="盘点方案-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsInventoryCountPlan>> queryPageList(WmsInventoryCountPlan wmsInventoryCountPlan,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsInventoryCountPlan> queryWrapper = QueryGenerator.initQueryWrapper(wmsInventoryCountPlan, req.getParameterMap());
		Page<WmsInventoryCountPlan> page = new Page<WmsInventoryCountPlan>(pageNo, pageSize);
		IPage<WmsInventoryCountPlan> pageList = wmsInventoryCountPlanService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 分页列表查询，包含盘点方案明细
	  * @param wmsInventoryCountPlanDTO
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  */
	@ApiOperation(value="盘点方案-分页列表查询", notes="盘点方案-分页列表查询")
	@GetMapping(value = "/listWithDetail")
	public Result<IPage<WmsInventoryCountPlanDTO>> queryPageListWithDetail(WmsInventoryCountPlanDTO wmsInventoryCountPlanDTO,
																			@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																			@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																			HttpServletRequest req) {
		QueryWrapper<WmsInventoryCountPlanDTO> queryWrapper = QueryGenerator.initQueryWrapper(wmsInventoryCountPlanDTO, req.getParameterMap());
		Page<WmsInventoryCountPlanDTO> page = new Page<WmsInventoryCountPlanDTO>(pageNo, pageSize);
		IPage<WmsInventoryCountPlanDTO> pageList = wmsInventoryCountPlanService.queryPageListWithDetail(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dto
	 * @return
	 */
	@AutoLog(value = "盘点方案-添加")
	@ApiOperation(value="盘点方案-添加", notes="盘点方案-添加")
	@RequiresPermissions("admin:wms_inventory_count_plan:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsInventoryCountPlanDTO dto) {
		wmsInventoryCountPlanService.saveInventoryCountPlan(dto);
		return Result.OK("添加成功！");
	}

	 /**
	  *  编辑
	  *
	  * @param dto
	  * @return
	  */
	@AutoLog(value = "盘点方案-编辑")
	@ApiOperation(value="盘点方案-编辑", notes="盘点方案-编辑")
	@RequiresPermissions("admin:wms_inventory_count_plan:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsInventoryCountPlanDTO dto) {
		wmsInventoryCountPlanService.updateInventoryCountPlan(dto);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "盘点方案-通过id删除")
	@ApiOperation(value="盘点方案-通过id删除", notes="盘点方案-通过id删除")
	@RequiresPermissions("admin:wms_inventory_count_plan:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsInventoryCountPlanService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "盘点方案-批量删除")
	@ApiOperation(value="盘点方案-批量删除", notes="盘点方案-批量删除")
	@RequiresPermissions("admin:wms_inventory_count_plan:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsInventoryCountPlanService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "盘点方案-通过id查询")
	@ApiOperation(value="盘点方案-通过id查询", notes="盘点方案-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsInventoryCountPlan> queryById(@RequestParam(name="id",required=true) String id) {
		WmsInventoryCountPlan wmsInventoryCountPlan = wmsInventoryCountPlanService.getById(id);
		if(wmsInventoryCountPlan==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsInventoryCountPlan);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsInventoryCountPlan
    */
    @RequiresPermissions("admin:wms_inventory_count_plan:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsInventoryCountPlan wmsInventoryCountPlan) {
        return super.exportXls(request, wmsInventoryCountPlan, WmsInventoryCountPlan.class, "盘点方案");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_inventory_count_plan:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WmsInventoryCountPlan.class);
    }


	 /**
	  * 执行方案
	  *
	  * @param dto
	  * @return
	  */
	 @AutoLog(value = "盘点方案-执行方案")
	 @ApiOperation(value="盘点方案-执行方案", notes="盘点方案-执行方案")
	 @RequiresPermissions("admin:wms_inventory_count_plan:execute")
	 @PostMapping(value = "/execute")
	 public Result<String> executePlan(@RequestBody WmsInventoryCountPlanDTO dto) {
		 wmsInventoryCountPlanService.executePlan(dto);
		 return Result.OK("方案执行成功！");
	 }

	 /**
	  * 废弃方案
	  *
	  * @param id 方案ID
	  * @return
	  */
	 @AutoLog(value = "盘点方案-废弃方案")
	 @ApiOperation(value="盘点方案-废弃方案", notes="盘点方案-废弃方案")
	 @RequiresPermissions("admin:wms_inventory_count_plan:discard")
	 @PostMapping(value = "/discard")
	 public Result<String> discardPlan(@RequestBody String id) {
		 wmsInventoryCountPlanService.discardPlan(id);
		 return Result.OK("方案废弃成功！");
	 }

}
