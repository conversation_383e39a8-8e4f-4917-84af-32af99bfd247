package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsPurchaseDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 采购订单明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsPurchaseDetailService extends IService<WmsPurchaseDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsPurchaseDetail>
	 */
	public List<WmsPurchaseDetail> selectByMainId(String mainId);
}
