package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsStockTransferDetail;
import org.jeecg.modules.admin.entity.WmsStockTransfer;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 库存调拨
 * @Author: jeecg-boot
 * @Date:   2025-01-04
 * @Version: V1.0
 */
public interface IWmsStockTransferService extends IService<WmsStockTransfer> {

	/**
	 * 添加一对多
	 *
	 * @param wmsStockTransfer
	 * @param wmsStockTransferDetailList
	 */
	public void saveMain(WmsStockTransfer wmsStockTransfer,List<WmsStockTransferDetail> wmsStockTransferDetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsStockTransfer
	 * @param wmsStockTransferDetailList
	 */
	public void updateMain(WmsStockTransfer wmsStockTransfer,List<WmsStockTransferDetail> wmsStockTransferDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	void createFormalData(String ids);

    void transfer(String id);
}
