package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface IWmsStockdetailService extends IService<WmsStockdetail> {

    IPage<WmsStockdetail> listSummary(WmsStockdetail wmsStockdetail, Integer pageNo, Integer pageSize, HttpServletRequest req);

    List<Map<String, Object>> getTop5();
    Map<String, Object> getWeekStock(String startTime, String endTime);

    List<WmsStockdetail> queryWithNullObDtlId(QueryWrapper<WmsStockdetail> queryWrapper,String levelNo);

    List<WmsStockdetail> queryWithNullObDtlIdForFlat(QueryWrapper<WmsStockdetail> queryWrapper);

    List<WmsStockdetail> queryWithNullObDtlIdForLine(QueryWrapper<WmsStockdetail> queryWrapper);

    IPage<WmsStockdetail> queryDistributionInfo(WmsStockdetail wmsStockdetail, Integer pageNo, Integer pageSize, Boolean isDetail, HttpServletRequest req);

    Result<?> importExcelForstockdetail(HttpServletRequest request, HttpServletResponse response, Class<WmsStockdetail> wmsStockdetailClass);

    /**标记库存临期或者超期 */
    void markStockExpired();    
}
