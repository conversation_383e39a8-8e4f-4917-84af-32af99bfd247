package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsSalereturnDraftDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 销售退货明细草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsSalereturnDraftDetailService extends IService<WmsSalereturnDraftDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsSalereturnDraftDetail>
	 */
	public List<WmsSalereturnDraftDetail> selectByMainId(String mainId);
}
