package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value="WmsShippingPlanPage对象", description="发货计划")
public class WmsShippingPlanBPage {
    //货运单号
    @ApiModelProperty(value = "货运单号")
    private String cargoNo;
    //客户名称
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    //品号
    @ApiModelProperty(value = "品号")
    private String itemNumber;
    //品名
    @ApiModelProperty(value = "品名")
    private String itemName;
    //批号
    @ApiModelProperty(value = "批号")
    private String batchCode;
    //数量
    @ApiModelProperty(value = "数量")
    private Double quantity;
    //单个重量
    @ApiModelProperty(value = "单个重量")
    private String unitWeight;
    //总重量
    @ApiModelProperty(value = "总重量")
    private BigDecimal totalWeight;
   //状态
    @ApiModelProperty(value = "状态")
    private String status;
    //备注
    @ApiModelProperty(value = "备注")
    private String remark;
}
