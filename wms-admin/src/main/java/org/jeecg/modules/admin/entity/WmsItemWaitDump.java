package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 待转储物料信息
 * @Author: jeecg-boot
 * @Date:   2024-11-21
 * @Version: V1.0
 */
@Data
@TableName("wms_item_wait_dump")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_item_wait_dump对象", description="待转储物料信息")
public class WmsItemWaitDump implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**倒扣领料单号*/
	@Excel(name = "倒扣领料单号", width = 15)
    @ApiModelProperty(value = "倒扣领料单号")
    private java.lang.String billNo;
	/**MES工单编号*/
	@Excel(name = "MES工单编号", width = 15)
    @ApiModelProperty(value = "MES工单编号")
    private java.lang.String workNo;
	/**待转入仓库*/
	@Excel(name = "待转入仓库", width = 15)
    @ApiModelProperty(value = "待转入仓库")
    private java.lang.String toWarehouse;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
	/**单据类型*/
    @Excel(name = "单据类型", width = 15, dicCode = "stock_dump_bill_type")
    @Dict(dicCode = "stock_dump_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**物料行号*/
	@Excel(name = "物料行号", width = 15)
    @ApiModelProperty(value = "物料行号")
    private java.lang.Integer lineNumber;
	/**生产计划数量*/
	@Excel(name = "生产计划数量", width = 15)
    @ApiModelProperty(value = "生产计划数量")
    private java.lang.Double mesPlanQty;
	/**线边仓库存数量*/
	@Excel(name = "线边仓库存数量", width = 15)
    @ApiModelProperty(value = "线边仓库存数量")
    private java.lang.Double stockQty;
	/**待转储数量*/
	@Excel(name = "待转储数量", width = 15)
    @ApiModelProperty(value = "待转储数量")
    private java.lang.Double qty;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "wms_item_wait_dump_status")
	@Dict(dicCode = "wms_item_wait_dump_status")
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**单据明细id*/
    @Excel(name = "单据明细id", width = 15)
    @ApiModelProperty(value = "单据明细id")
    private java.lang.String srcLineId;
}
