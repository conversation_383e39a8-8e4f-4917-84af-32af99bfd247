package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsItemWaitDump;
import org.jeecg.modules.admin.mapper.WmsItemWaitDumpMapper;
import org.jeecg.modules.admin.service.IWmsItemWaitDumpService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 待转储物料信息
 * @Author: jeecg-boot
 * @Date:   2024-11-21
 * @Version: V1.0
 */
@Service
public class WmsItemWaitDumpServiceImpl extends ServiceImpl<WmsItemWaitDumpMapper, WmsItemWaitDump> implements IWmsItemWaitDumpService {

}
