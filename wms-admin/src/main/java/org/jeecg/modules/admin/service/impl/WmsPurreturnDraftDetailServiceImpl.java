package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsPurreturnDraftDetail;
import org.jeecg.modules.admin.mapper.WmsPurreturnDraftDetailMapper;
import org.jeecg.modules.admin.service.IWmsPurreturnDraftDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 采购退货草稿明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Service
public class WmsPurreturnDraftDetailServiceImpl extends ServiceImpl<WmsPurreturnDraftDetailMapper, WmsPurreturnDraftDetail> implements IWmsPurreturnDraftDetailService {
	
	@Autowired
	private WmsPurreturnDraftDetailMapper wmsPurreturnDraftDetailMapper;
	
	@Override
	public List<WmsPurreturnDraftDetail> selectByMainId(String mainId) {
		return wmsPurreturnDraftDetailMapper.selectByMainId(mainId);
	}
}
