package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 业务伙伴信息
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Data
@TableName("wms_business_partner")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_business_partner对象", description="业务伙伴信息")
public class WmsBusinessPartner implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**业务伙伴编号*/
	@Excel(name = "业务伙伴编号", width = 15)
    @ApiModelProperty(value = "业务伙伴编号")
    private java.lang.String partnerCode;
	/**业务伙伴名称*/
	@Excel(name = "业务伙伴名称", width = 15)
    @ApiModelProperty(value = "业务伙伴名称")
    private java.lang.String partnerName;
	/**英文简称*/
	@Excel(name = "英文简称", width = 15)
    @ApiModelProperty(value = "英文简称")
    private java.lang.String englishName;
	/**业务伙伴类型*/
	@Excel(name = "业务伙伴类型", width = 15, dicCode = "partner_type")
	@Dict(dicCode = "partner_type")
    @ApiModelProperty(value = "业务伙伴类型")
    private java.lang.String partnerType;
	/**地址*/
	@Excel(name = "地址", width = 15)
    @ApiModelProperty(value = "地址")
    private java.lang.String address;
	/**联系人*/
	@Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String contact;
	/**联系人电话*/
	@Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String phone;
	/**联系人邮箱*/
	@Excel(name = "联系人邮箱", width = 15)
    @ApiModelProperty(value = "联系人邮箱")
    private java.lang.String email;
	/**可用标记*/
	@Excel(name = "可用标记", width = 15, dicCode = "partner_enable_flag")
	@Dict(dicCode = "partner_enable_flag")
    @ApiModelProperty(value = "可用标记")
    private java.lang.String enableFlag;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
    /**账套信息*/
    @Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
}
