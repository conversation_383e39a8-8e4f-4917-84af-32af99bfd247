package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsPurchase;
import org.jeecg.modules.admin.entity.WmsPurchaseDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 采购订单
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_purchasePage对象", description="采购订单")
public class WmsPurchasePage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
	@ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
	/**采购订单号*/
	@Excel(name = "采购订单号", width = 15)
	@ApiModelProperty(value = "采购订单号")
    private java.lang.String workNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "purchase_bill_type")
    @Dict(dicCode = "purchase_bill_type")
	@ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**供应商编号*/
	@Excel(name = "供应商编号", width = 15)
	@ApiModelProperty(value = "供应商编号")
    private java.lang.String supplierCode;
	/**供应商名称*/
	@Excel(name = "供应商名称", width = 15)
	@ApiModelProperty(value = "供应商名称")
    private java.lang.String supplierName;
	/**批号*/
	@Excel(name = "批号", width = 15)
	@ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**过账日期*/
	@Excel(name = "过账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "过账日期")
    private java.util.Date postDate;
	/**交货日期*/
	@Excel(name = "交货日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "交货日期")
    private java.util.Date deliveryDate;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "purchase_bill_status")
    @Dict(dicCode = "purchase_bill_status")
	@ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
	@ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15)
	@ApiModelProperty(value = "账套信息")
	private java.lang.String accountCode;
	
	@ExcelCollection(name="采购订单明细")
	@ApiModelProperty(value = "采购订单明细")
	private List<WmsPurchaseDetail> wmsPurchaseDetailList;
	
}
