package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.service.IWmsApiService;
import org.jeecg.modules.admin.vo.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class WmsApiServiceImpl implements IWmsApiService {

    @Value("${wms.api.base-url}")
    private String baseUrl;

    private JSONObject executeRequest(String url, Object requestObject) {
        System.out.println("Request: " + JSON.toJSONString(requestObject));
        JSONObject result = new JSONObject();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            String requestBody = JSON.toJSONString(requestObject);
            post.setEntity(new StringEntity(requestBody));

            try (CloseableHttpResponse response = httpClient.execute(post)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (response.getStatusLine().getStatusCode() == 200) {
                    System.out.println(responseBody);
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    int code = jsonObject.getIntValue("Code");
                    String message = jsonObject.getString("Message");
                    result.put("message", message);
                    System.out.println("Response: " + responseBody);
                    if (WmsConstant.WmsApiResultEnum.SUCCESS.getValue().equals(String.valueOf(code))) {
                        result.put("result", true);
                    } else {
                        result.put("result", false);
                    }
                } else {
                    result.put("result", false);
                    result.put("message", "Request failed: " + response.getStatusLine().getStatusCode() + ". Response: " + responseBody);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            result.put("result", false);
            result.put("message", "IOException occurred: " + e.getMessage());
        }
        return result;
    }

    @Override
    public JSONObject proOutSelect(OutSelectPage outSelectPage) {
        String url = baseUrl + "/ProOutSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject proOutSend(OutSendPage outSendPage) {
        String url = baseUrl + "/ProOutSend";
        return executeRequest(url, outSendPage);
    }

    @Override
    public JSONObject proOutCancelSelect(OutSelectPage outSelectPage) {
        String url = baseUrl + "/ProOutCancelSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject stockMark(StockMarkPage stockMarkPage) {
        String url = baseUrl + "/StockMark";
        return executeRequest(url, stockMarkPage);
    }

    @Override
    public JSONObject stockCancel(StockMarkPage stockMarkPage) {
        String url = baseUrl + "/StockCancel";
        return executeRequest(url, stockMarkPage);
    }

    @Override
    public JSONObject stoForceOut(StoForceOutPage stoForceOutPage) {
        String url = baseUrl + "/StoForceOut";
        return executeRequest(url, stoForceOutPage);
    }

    @Override
    public JSONObject taskForceDone(TaskForceDonePage taskForceDonePage) {
        String url = baseUrl + "/TaskForceDone";
        return executeRequest(url, taskForceDonePage);
    }

    @Override
    public JSONObject taskPriority(TaskPriorityPage taskPriorityPage) {
        String url = baseUrl + "/TaskPriority";
        return executeRequest(url, taskPriorityPage);
    }

    @Override
    public JSONObject rawOutSelect(OutSelectPage outSelectPage) {
        String url = baseUrl + "/RawOutSelect";
        return executeRequest(url, outSelectPage);
    }

    @Override
    public JSONObject rawOutSend(OutSendPage outSendPage) {
        String url = baseUrl + "/RawOutSend";
        return executeRequest(url, outSendPage);
    }

    @Override
    public JSONObject stockCheck(String itemCode, String state) {
        String url = baseUrl + "/StockCheck";
        JSONObject requestObject = new JSONObject();
        requestObject.put("item_code", itemCode);
        requestObject.put("state", state);
        return executeRequest(url, requestObject);
    }

    @Override
    public JSONObject stockMove(StockMovePage stockMovePage) {
        String url = baseUrl + "/ContainerTransfer";
        return executeRequest(url, stockMovePage);
    }

    @Override
    public JSONObject rawOutBoundBack(BoundBackPage boundBackPage) {
        String url = baseUrl + "/RawOutBoundBack";
        return executeRequest(url, boundBackPage);
    }
}
