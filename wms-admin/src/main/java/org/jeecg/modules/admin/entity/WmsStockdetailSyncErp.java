package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: erp库存同步
 * @Author: jeecg-boot
 * @Date:   2024-09-11
 * @Version: V1.0
 */
@Data
@TableName("wms_stockdetail_sync_erp")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_stockdetail_sync_erp对象", description="erp库存同步")
public class WmsStockdetailSyncErp implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**容器条码*/
	@Excel(name = "容器条码", width = 15)
    @ApiModelProperty(value = "容器条码")
    private java.lang.String containerBarcode;
	/**同步状态*/
	@Excel(name = "同步状态", width = 15)
    @ApiModelProperty(value = "同步状态")
    private java.lang.String syncState;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**物料规格*/
	@Excel(name = "物料规格", width = 15)
    @ApiModelProperty(value = "物料规格")
    private java.lang.String itemSpec;
	/**物料单位*/
	@Excel(name = "物料单位", width = 15)
    @ApiModelProperty(value = "物料单位")
    private java.lang.String itemUnit;
	/**物料种类*/
	@Excel(name = "物料种类", width = 15)
    @ApiModelProperty(value = "物料种类")
    private java.lang.String itemKind;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**物料条码*/
	@Excel(name = "物料条码", width = 15)
    @ApiModelProperty(value = "物料条码")
    private java.lang.String itemBarcode;
	/**生产日期*/
	@Excel(name = "生产日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生产日期")
    private java.util.Date productDate;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @ApiModelProperty(value = "数量")
    private java.lang.Double quantity;
	/**工单编号*/
	@Excel(name = "工单编号", width = 15)
    @ApiModelProperty(value = "工单编号")
    private java.lang.String workNo;
	/**销售单号*/
	@Excel(name = "销售单号", width = 15)
    @ApiModelProperty(value = "销售单号")
    private java.lang.String sendBillNo;
	/**物料类型*/
	@Excel(name = "物料类型", width = 15, dicCode = "materiel_type")
	@Dict(dicCode = "materiel_type")
    @ApiModelProperty(value = "物料类型")
    private java.lang.String materielType;
	/**区域*/
	@Excel(name = "区域", width = 15)
    @ApiModelProperty(value = "区域")
    private java.lang.String zoneCode;
	/**货位编号*/
	@Excel(name = "货位编号", width = 15)
    @ApiModelProperty(value = "货位编号")
    private java.lang.String locateCode;
}
