package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 物料信息
 * @Author: jeecg-boot
 * @Date:   2024-07-15
 * @Version: V1.0
 */
@Data
@TableName("wms_spec_match_item")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_spec_match_item对象", description="物料信息")
public class WmsSpecMatchItem implements Serializable {
    private static final long serialVersionUID = 1L;

	/**唯一标志*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "唯一标志")
    private java.lang.String id;
	/**物料编号*/
	@Excel(name = "物料编号", width = 15)
    @ApiModelProperty(value = "物料编号")
    private java.lang.String itemCode;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
    @ApiModelProperty(value = "物料名称")
    private java.lang.String itemName;
    /**物料简称*/
    @Excel(name = "物料简称", width = 15)
    @ApiModelProperty(value = "物料简称")
    private java.lang.String itemNameShort;
	/**规格代码*/
	@Excel(name = "规格代码", width = 15)
    @ApiModelProperty(value = "规格代码")
    private java.lang.String specCode;
	/**规格名称*/
	@Excel(name = "规格名称", width = 15)
    @ApiModelProperty(value = "规格名称")
    private java.lang.String specName;
    /**单位*/
    @Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**预警值上限*/
	@Excel(name = "预警值上限", width = 15)
    @ApiModelProperty(value = "预警值上限")
    private java.lang.Double maxValue;
	/**预警值下限*/
	@Excel(name = "预警值下限", width = 15)
    @ApiModelProperty(value = "预警值下限")
    private java.lang.Double minValue;
	/**是否有效*/
	@Excel(name = "是否启用", width = 15, dicCode = "enable_flag")
	@Dict(dicCode = "enable_flag")
    @ApiModelProperty(value = "是否启用")
    private java.lang.String enableFlag;
	/**免检标记*/
	@Excel(name = "免检标记", width = 15, dicCode = "free_flag")
	@Dict(dicCode = "free_flag")
    @ApiModelProperty(value = "免检标记")
    private java.lang.String freeFlag;
	/**来自ERP*/
	@Excel(name = "来自ERP", width = 15, dicCode = "from_erp")
	@Dict(dicCode = "from_erp")
    @ApiModelProperty(value = "来自ERP")
    private java.lang.String fromErp;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private java.lang.String createby;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**物料编码*/
	@Excel(name = "物料编码(弃用)", width = 15)
    @ApiModelProperty(value = "物料编码(弃用)")
    private java.lang.String materialCode;
	/**物料名称*/
	@Excel(name = "物料名称(弃用)", width = 15)
    @ApiModelProperty(value = "物料名称(弃用)")
    private java.lang.String materialName;
	/**物料类型*/
	@Excel(name = "物料类型", width = 15, dicCode = "materiel_type")
    @ApiModelProperty(value = "物料类型")
    @Dict(dicCode = "materiel_type")
    private java.lang.String materialType;
    /**默认仓库编号*/
    @Excel(name = "默认仓库编号", width = 15)
    @ApiModelProperty(value = "默认仓库编号")
    private java.lang.String warehouseCode;
    /**发货方法*/
    @Excel(name = "发货方法", width = 15, dicCode = "delivery_method")
    @ApiModelProperty(value = "发货方法")
    @Dict(dicCode = "delivery_method")
    private java.lang.String deliveryMethod;
    /**管理方法*/
    @Excel(name = "管理方法", width = 15, dicCode = "manage_method")
    @ApiModelProperty(value = "管理方法")
    @Dict(dicCode = "manage_method")
    private java.lang.String manageMethod;
    /**物料组代码*/
    @Excel(name = "物料组代码", width = 15)
    @ApiModelProperty(value = "物料组代码")
    private java.lang.String groupCode;
    /**物料组名称*/
    @Excel(name = "物料组名称", width = 15)
    @ApiModelProperty(value = "物料组名称")
    private java.lang.String groupName;
    /**账套信息*/
    @Excel(name = "账套信息", width = 15,dicCode = "account_code")
    @ApiModelProperty(value = "账套信息")
    @Dict(dicCode = "account_code")
    private java.lang.String accountCode;
    /**是否采购物料*/
    @Excel(name = "是否采购物料", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "是否采购物料")
    @Dict(dicCode = "yn")
    private java.lang.String purchaseFlag;
    /**是否销售物料*/
    @Excel(name = "是否销售物料", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "是否销售物料")
    @Dict(dicCode = "yn")
    private java.lang.String saleFlag;
    /**是否仓库物料*/
    @Excel(name = "是否仓库物料", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "是否仓库物料")
    @Dict(dicCode = "yn")
    private java.lang.String warehouseFlag;
    /**价格*/
    @Excel(name = "价格", width = 15)
    @ApiModelProperty(value = "价格")
    private java.lang.Double price;
    /**保质期*/
    @Excel(name = "保质期", width = 15)
    @ApiModelProperty(value = "保质期")
    private java.lang.Double shelfLife;
}
