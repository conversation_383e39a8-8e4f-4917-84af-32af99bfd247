package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.service.IWmsStockdReceiveSendService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: WMS库存收发
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
@Api(tags="WMS库存收发")
@RestController
@RequestMapping("/admin/wmsStockdReceiveSend")
@Slf4j
public class WmsStockdReceiveSendController extends JeecgController<WmsStockdReceiveSend, IWmsStockdReceiveSendService> {
	@Autowired
	private IWmsStockdReceiveSendService wmsStockdReceiveSendService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wmsStockdReceiveSend
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "WMS库存收发-分页列表查询")
	@ApiOperation(value="WMS库存收发-分页列表查询", notes="WMS库存收发-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsStockdReceiveSend>> queryPageList(WmsStockdReceiveSend wmsStockdReceiveSend,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<WmsStockdReceiveSend> queryWrapper = QueryGenerator.initQueryWrapper(wmsStockdReceiveSend, req.getParameterMap());
		Page<WmsStockdReceiveSend> page = new Page<WmsStockdReceiveSend>(pageNo, pageSize);
		IPage<WmsStockdReceiveSend> pageList = wmsStockdReceiveSendService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param wmsStockdReceiveSend
	 * @return
	 */
	@AutoLog(value = "WMS库存收发-添加")
	@ApiOperation(value="WMS库存收发-添加", notes="WMS库存收发-添加")
	@RequiresPermissions("admin:wms_stockd_receive_send:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsStockdReceiveSend wmsStockdReceiveSend) {
		wmsStockdReceiveSend.setApproveStatus("0");
		wmsStockdReceiveSendService.saveWithBarcode(wmsStockdReceiveSend);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsStockdReceiveSend
	 * @return
	 */
	@AutoLog(value = "WMS库存收发-编辑")
	@ApiOperation(value="WMS库存收发-编辑", notes="WMS库存收发-编辑")
	@RequiresPermissions("admin:wms_stockd_receive_send:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsStockdReceiveSend wmsStockdReceiveSend) {
		wmsStockdReceiveSendService.updateById(wmsStockdReceiveSend);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "WMS库存收发-通过id删除")
	@ApiOperation(value="WMS库存收发-通过id删除", notes="WMS库存收发-通过id删除")
	@RequiresPermissions("admin:wms_stockd_receive_send:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsStockdReceiveSendService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "WMS库存收发-批量删除")
	@ApiOperation(value="WMS库存收发-批量删除", notes="WMS库存收发-批量删除")
	@RequiresPermissions("admin:wms_stockd_receive_send:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsStockdReceiveSendService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "WMS库存收发-通过id查询")
	@ApiOperation(value="WMS库存收发-通过id查询", notes="WMS库存收发-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsStockdReceiveSend> queryById(@RequestParam(name="id",required=true) String id) {
		WmsStockdReceiveSend wmsStockdReceiveSend = wmsStockdReceiveSendService.getById(id);
		if(wmsStockdReceiveSend==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsStockdReceiveSend);
	}

	 /**
	  * 通过ids进行审核
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "WMS库存收发-通过ids进行审核")
	 @ApiOperation(value="WMS库存收发-通过ids进行审核", notes="WMS库存收发-通过ids进行审核")
	 @GetMapping(value = "/handleAudit")
	 public Result<String> handleAudit(@RequestParam(name="ids",required=true) String ids) {
		 wmsStockdReceiveSendService.handleAudit(ids);
		 return Result.OK("审核成功!");
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param wmsStockdReceiveSend
    */
    @RequiresPermissions("admin:wms_stockd_receive_send:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsStockdReceiveSend wmsStockdReceiveSend) {
        return super.exportXls(request, wmsStockdReceiveSend, WmsStockdReceiveSend.class, "WMS库存收发");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_stockd_receive_send:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<WmsStockdReceiveSend> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsStockdReceiveSend.class, params);
                // 调用自定义方法处理导入数据并生成唯一物料条码
                int successCount = wmsStockdReceiveSendService.importWithBarcode(list);
                return Result.OK("文件导入成功！共导入" + successCount + "条数据");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.error("文件导入失败！");
    }

}
