package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsSalereturnDraft;
import org.jeecg.modules.admin.entity.WmsSalereturnDraftDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 销售退货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_salereturn_draftPage对象", description="销售退货草稿")
public class WmsSalereturnDraftPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
	@ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
	/**销售退货单号*/
	@Excel(name = "销售退货单号", width = 15)
	@ApiModelProperty(value = "销售退货单号")
    private java.lang.String workNo;
	/**草稿号*/
	@Excel(name = "草稿号", width = 15)
	@ApiModelProperty(value = "草稿号")
    private java.lang.String draftNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "salereturn_draft_bill_type")
    @Dict(dicCode = "salereturn_draft_bill_type")
	@ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**客户编号*/
	@Excel(name = "客户编号", width = 15)
	@ApiModelProperty(value = "客户编号")
    private java.lang.String customerCode;
	/**客户名称*/
	@Excel(name = "客户名称", width = 15)
	@ApiModelProperty(value = "客户名称")
    private java.lang.String customerName;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "salereturn_draft_bill_status")
    @Dict(dicCode = "salereturn_draft_bill_status")
	@ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
    @Dict(dicCode = "from_erp")
	@ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**过账日期*/
	@Excel(name = "过账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "过账日期")
    private java.util.Date postDate;
	/**交货日期*/
	@Excel(name = "交货日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "交货日期")
    private java.util.Date deliveryDate;
	/**交货类型*/
	@Excel(name = "交货类型", width = 15)
	@ApiModelProperty(value = "交货类型")
    private java.lang.String deliveryType;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15)
	@ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
	/**交货备注*/
	@Excel(name = "交货备注", width = 15)
	@ApiModelProperty(value = "交货备注")
    private java.lang.String remark;
	/**交货备注2*/
	@Excel(name = "交货备注2", width = 15)
	@ApiModelProperty(value = "交货备注2")
    private java.lang.String remark2;
	/**正式数据标识*/
	@Excel(name = "正式数据标识", width = 15, dicCode = "formal_data_flag")
	@Dict(dicCode = "formal_data_flag")
	@ApiModelProperty(value = "正式数据标识")
	private java.lang.String formalDataFlag;
	
	@ExcelCollection(name="销售退货明细草稿")
	@ApiModelProperty(value = "销售退货明细草稿")
	private List<WmsSalereturnDraftDetail> wmsSalereturnDraftDetailList;
	
}
