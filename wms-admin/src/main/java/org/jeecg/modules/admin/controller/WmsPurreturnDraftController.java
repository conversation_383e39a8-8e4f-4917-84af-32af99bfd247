package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.admin.service.IWmsReceiveService;
import org.jeecg.modules.admin.service.IWmsReceivedetailService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsPurreturnDraftDetail;
import org.jeecg.modules.admin.entity.WmsPurreturnDraft;
import org.jeecg.modules.admin.vo.WmsPurreturnDraftPage;
import org.jeecg.modules.admin.service.IWmsPurreturnDraftService;
import org.jeecg.modules.admin.service.IWmsPurreturnDraftDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 采购退货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Api(tags="采购退货草稿")
@RestController
@RequestMapping("/admin/wmsPurreturnDraft")
@Slf4j
public class WmsPurreturnDraftController {
	@Autowired
	private IWmsPurreturnDraftService wmsPurreturnDraftService;
	@Autowired
	private IWmsPurreturnDraftDetailService wmsPurreturnDraftDetailService;


	 /**
	  * 采购退货草稿-分页列表查询或详情查询
	  *
	  * @param wmsPurreturnDraft 采购退货草稿实体
	  * @param pageNo            页码
	  * @param pageSize          每页条数
	  * @param itemCode          物品编码（可选）
	  * @param itemName          物品名称（可选）
	  * @param req               HTTP 请求
	  * @return 分页结果或详情列表
	  */
	 @ApiOperation(value = "采购退货草稿-分页列表查询或详情查询", notes = "根据参数决定分页列表查询或详情查询")
	 @GetMapping("/list")
	 public Result<IPage<WmsPurreturnDraft>> queryPageList(
			 WmsPurreturnDraft wmsPurreturnDraft,
			 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			 @RequestParam(name = "itemCode", required = false) String itemCode,
			 @RequestParam(name = "itemName", required = false) String itemName,
			 HttpServletRequest req) {

		 // 初始化查询条件
		 QueryWrapper<WmsPurreturnDraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsPurreturnDraft, req.getParameterMap());

		 Page<WmsPurreturnDraft> page = new Page<>(pageNo, pageSize);

		 // 判断是否包含 itemCode 或 itemName 参数
		 if (StringUtils.isNotBlank(itemCode) || StringUtils.isNotBlank(itemName)) {
			 // 调用按物品查询的逻辑
			 IPage<WmsPurreturnDraft> detailPage = wmsPurreturnDraftService.queryPurreturnDraftByItem(page, itemCode, itemName, queryWrapper);
			 return Result.OK(detailPage);
		 } else {
			 // 调用原有的分页查询逻辑
			 IPage<WmsPurreturnDraft> pageList = wmsPurreturnDraftService.page(page, queryWrapper);
			 return Result.OK(pageList);
		 }
	 }
	
	/**
	 *   添加
	 *
	 * @param wmsPurreturnDraftPage
	 * @return
	 */
	@AutoLog(value = "采购退货草稿-添加")
	@ApiOperation(value="采购退货草稿-添加", notes="采购退货草稿-添加")
    @RequiresPermissions("admin:wms_purreturn_draft:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsPurreturnDraftPage wmsPurreturnDraftPage) {
		WmsPurreturnDraft wmsPurreturnDraft = new WmsPurreturnDraft();
		BeanUtils.copyProperties(wmsPurreturnDraftPage, wmsPurreturnDraft);
		wmsPurreturnDraftService.saveMain(wmsPurreturnDraft, wmsPurreturnDraftPage.getWmsPurreturnDraftDetailList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsPurreturnDraftPage
	 * @return
	 */
	@AutoLog(value = "采购退货草稿-编辑")
	@ApiOperation(value="采购退货草稿-编辑", notes="采购退货草稿-编辑")
    @RequiresPermissions("admin:wms_purreturn_draft:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsPurreturnDraftPage wmsPurreturnDraftPage) {
		WmsPurreturnDraft wmsPurreturnDraft = new WmsPurreturnDraft();
		BeanUtils.copyProperties(wmsPurreturnDraftPage, wmsPurreturnDraft);
		WmsPurreturnDraft wmsPurreturnDraftEntity = wmsPurreturnDraftService.getById(wmsPurreturnDraft.getId());
		if(wmsPurreturnDraftEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsPurreturnDraftService.updateMain(wmsPurreturnDraft, wmsPurreturnDraftPage.getWmsPurreturnDraftDetailList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "采购退货草稿-通过id删除")
	@ApiOperation(value="采购退货草稿-通过id删除", notes="采购退货草稿-通过id删除")
    @RequiresPermissions("admin:wms_purreturn_draft:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsPurreturnDraftService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "采购退货草稿-批量删除")
	@ApiOperation(value="采购退货草稿-批量删除", notes="采购退货草稿-批量删除")
    @RequiresPermissions("admin:wms_purreturn_draft:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsPurreturnDraftService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "采购退货草稿-通过id查询")
	@ApiOperation(value="采购退货草稿-通过id查询", notes="采购退货草稿-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsPurreturnDraft> queryById(@RequestParam(name="id",required=true) String id) {
		WmsPurreturnDraft wmsPurreturnDraft = wmsPurreturnDraftService.getById(id);
		if(wmsPurreturnDraft==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsPurreturnDraft);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "采购退货草稿明细-通过主表ID查询")
	@ApiOperation(value="采购退货草稿明细-通过主表ID查询", notes="采购退货草稿明细-通过主表ID查询")
	@GetMapping(value = "/queryWmsPurreturnDraftDetailByMainId")
	public Result<IPage<WmsPurreturnDraftDetail>> queryWmsPurreturnDraftDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList = wmsPurreturnDraftDetailService.selectByMainId(id);
		IPage <WmsPurreturnDraftDetail> page = new Page<>();
		page.setRecords(wmsPurreturnDraftDetailList);
		page.setTotal(wmsPurreturnDraftDetailList.size());
		return Result.OK(page);
	}

	 /**
	  * 将草稿数据生成正式数据
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "采购退货草稿-生成正式数据")
	 @ApiOperation(value="采购退货草稿-生成正式数据", notes="采购退货草稿-生成正式数据")
	 @GetMapping(value = "/createFormalData")
	 public Result<String> createFormalData(@RequestParam(name = "ids", required = true) String ids) {
		 wmsPurreturnDraftService.createFormalData(ids);
		 return Result.OK("生成正式数据成功！");
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param wmsPurreturnDraft
    */
    @RequiresPermissions("admin:wms_purreturn_draft:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsPurreturnDraft wmsPurreturnDraft) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsPurreturnDraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsPurreturnDraft, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<WmsPurreturnDraft>  wmsPurreturnDraftList = wmsPurreturnDraftService.list(queryWrapper);

      // Step.3 组装pageList
      List<WmsPurreturnDraftPage> pageList = new ArrayList<WmsPurreturnDraftPage>();
      for (WmsPurreturnDraft main : wmsPurreturnDraftList) {
          WmsPurreturnDraftPage vo = new WmsPurreturnDraftPage();
          BeanUtils.copyProperties(main, vo);
          List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList = wmsPurreturnDraftDetailService.selectByMainId(main.getId());
          vo.setWmsPurreturnDraftDetailList(wmsPurreturnDraftDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "采购退货草稿列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsPurreturnDraftPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("采购退货草稿数据", "导出人:"+sysUser.getRealname(), "采购退货草稿"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_purreturn_draft:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsPurreturnDraftPage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsPurreturnDraftPage.class, params);
              for (WmsPurreturnDraftPage page : list) {
                  WmsPurreturnDraft po = new WmsPurreturnDraft();
                  BeanUtils.copyProperties(page, po);
                  wmsPurreturnDraftService.saveMain(po, page.getWmsPurreturnDraftDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
