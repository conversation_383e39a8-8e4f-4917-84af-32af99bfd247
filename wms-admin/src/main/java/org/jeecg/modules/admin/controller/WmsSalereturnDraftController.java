package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsSalereturnDraftDetail;
import org.jeecg.modules.admin.entity.WmsSalereturnDraft;
import org.jeecg.modules.admin.vo.WmsSalereturnDraftPage;
import org.jeecg.modules.admin.service.IWmsSalereturnDraftService;
import org.jeecg.modules.admin.service.IWmsSalereturnDraftDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 销售退货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Api(tags="销售退货草稿")
@RestController
@RequestMapping("/admin/wmsSalereturnDraft")
@Slf4j
public class WmsSalereturnDraftController {
	@Autowired
	private IWmsSalereturnDraftService wmsSalereturnDraftService;
	@Autowired
	private IWmsSalereturnDraftDetailService wmsSalereturnDraftDetailService;

	 /**
	  * 销售退货草稿-分页列表查询或详情查询
	  *
	  * @param wmsSalereturnDraft 销售退货草稿实体
	  * @param pageNo             页码
	  * @param pageSize           每页条数
	  * @param itemCode           物品编码（可选）
	  * @param itemName           物品名称（可选）
	  * @param req                HTTP 请求
	  * @return 分页结果或详情列表
	  */
	 @ApiOperation(value = "销售退货草稿-分页列表查询或详情查询", notes = "根据参数决定分页列表查询或详情查询")
	 @GetMapping("/list")
	 public Result<IPage<WmsSalereturnDraft>> queryPageList(
			 WmsSalereturnDraft wmsSalereturnDraft,
			 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			 @RequestParam(name = "itemCode", required = false) String itemCode,
			 @RequestParam(name = "itemName", required = false) String itemName,
			 HttpServletRequest req) {

		 // 初始化查询条件
		 QueryWrapper<WmsSalereturnDraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsSalereturnDraft, req.getParameterMap());
		 Page<WmsSalereturnDraft> page = new Page<>(pageNo, pageSize);

		 // 判断是否包含 itemCode 或 itemName 参数
		 if (StringUtils.isNotBlank(itemCode) || StringUtils.isNotBlank(itemName)) {
			 // 调用按物品查询的逻辑
			 IPage<WmsSalereturnDraft> detailPage = wmsSalereturnDraftService.querySalereturnDraftByItem(page, itemCode, itemName, queryWrapper);
			 return Result.OK(detailPage);
		 } else {
			 // 调用原有的分页查询逻辑
			 IPage<WmsSalereturnDraft> pageList = wmsSalereturnDraftService.page(page, queryWrapper);
			 return Result.OK(pageList);
		 }
	 }
	
	/**
	 *   添加
	 *
	 * @param wmsSalereturnDraftPage
	 * @return
	 */
	@AutoLog(value = "销售退货草稿-添加")
	@ApiOperation(value="销售退货草稿-添加", notes="销售退货草稿-添加")
    @RequiresPermissions("admin:wms_salereturn_draft:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsSalereturnDraftPage wmsSalereturnDraftPage) {
		WmsSalereturnDraft wmsSalereturnDraft = new WmsSalereturnDraft();
		BeanUtils.copyProperties(wmsSalereturnDraftPage, wmsSalereturnDraft);
		wmsSalereturnDraftService.saveMain(wmsSalereturnDraft, wmsSalereturnDraftPage.getWmsSalereturnDraftDetailList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param wmsSalereturnDraftPage
	 * @return
	 */
	@AutoLog(value = "销售退货草稿-编辑")
	@ApiOperation(value="销售退货草稿-编辑", notes="销售退货草稿-编辑")
    @RequiresPermissions("admin:wms_salereturn_draft:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsSalereturnDraftPage wmsSalereturnDraftPage) {
		WmsSalereturnDraft wmsSalereturnDraft = new WmsSalereturnDraft();
		BeanUtils.copyProperties(wmsSalereturnDraftPage, wmsSalereturnDraft);
		WmsSalereturnDraft wmsSalereturnDraftEntity = wmsSalereturnDraftService.getById(wmsSalereturnDraft.getId());
		if(wmsSalereturnDraftEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsSalereturnDraftService.updateMain(wmsSalereturnDraft, wmsSalereturnDraftPage.getWmsSalereturnDraftDetailList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "销售退货草稿-通过id删除")
	@ApiOperation(value="销售退货草稿-通过id删除", notes="销售退货草稿-通过id删除")
    @RequiresPermissions("admin:wms_salereturn_draft:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsSalereturnDraftService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "销售退货草稿-批量删除")
	@ApiOperation(value="销售退货草稿-批量删除", notes="销售退货草稿-批量删除")
    @RequiresPermissions("admin:wms_salereturn_draft:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsSalereturnDraftService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "销售退货草稿-通过id查询")
	@ApiOperation(value="销售退货草稿-通过id查询", notes="销售退货草稿-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsSalereturnDraft> queryById(@RequestParam(name="id",required=true) String id) {
		WmsSalereturnDraft wmsSalereturnDraft = wmsSalereturnDraftService.getById(id);
		if(wmsSalereturnDraft==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsSalereturnDraft);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "销售退货明细草稿-通过主表ID查询")
	@ApiOperation(value="销售退货明细草稿-通过主表ID查询", notes="销售退货明细草稿-通过主表ID查询")
	@GetMapping(value = "/queryWmsSalereturnDraftDetailByMainId")
	public Result<IPage<WmsSalereturnDraftDetail>> queryWmsSalereturnDraftDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsSalereturnDraftDetail> wmsSalereturnDraftDetailList = wmsSalereturnDraftDetailService.selectByMainId(id);
		IPage <WmsSalereturnDraftDetail> page = new Page<>();
		page.setRecords(wmsSalereturnDraftDetailList);
		page.setTotal(wmsSalereturnDraftDetailList.size());
		return Result.OK(page);
	}

	 /**
	  * 将草稿数据生成正式数据
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "销售退货草稿-生成正式数据")
	 @ApiOperation(value="销售退货草稿-生成正式数据", notes="销售退货草稿-生成正式数据")
	 @GetMapping(value = "/createFormalSalesData")
	 public Result<String> createFormalSalesData(@RequestParam(name="ids",required=true) String ids) {
		 wmsSalereturnDraftService.createFormalSaleReturnData(ids);
		 return Result.OK("生成正式数据成功！");
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param wmsSalereturnDraft
    */
    @RequiresPermissions("admin:wms_salereturn_draft:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsSalereturnDraft wmsSalereturnDraft) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsSalereturnDraft> queryWrapper = QueryGenerator.initQueryWrapper(wmsSalereturnDraft, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<WmsSalereturnDraft>  wmsSalereturnDraftList = wmsSalereturnDraftService.list(queryWrapper);

      // Step.3 组装pageList
      List<WmsSalereturnDraftPage> pageList = new ArrayList<WmsSalereturnDraftPage>();
      for (WmsSalereturnDraft main : wmsSalereturnDraftList) {
          WmsSalereturnDraftPage vo = new WmsSalereturnDraftPage();
          BeanUtils.copyProperties(main, vo);
          List<WmsSalereturnDraftDetail> wmsSalereturnDraftDetailList = wmsSalereturnDraftDetailService.selectByMainId(main.getId());
          vo.setWmsSalereturnDraftDetailList(wmsSalereturnDraftDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "销售退货草稿列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsSalereturnDraftPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("销售退货草稿数据", "导出人:"+sysUser.getRealname(), "销售退货草稿"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_salereturn_draft:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsSalereturnDraftPage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsSalereturnDraftPage.class, params);
              for (WmsSalereturnDraftPage page : list) {
                  WmsSalereturnDraft po = new WmsSalereturnDraft();
                  BeanUtils.copyProperties(page, po);
                  wmsSalereturnDraftService.saveMain(po, page.getWmsSalereturnDraftDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
