package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.SapApiConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: SAP api配置
 * @Author: jeecg-boot
 * @Date:   2025-03-19
 * @Version: V1.0
 */
public interface ISapApiConfigService extends IService<SapApiConfig> {
    
    /**
     * 根据API类型查询配置列表
     * @param apiType API类型
     * @return 配置列表
     */
    List<SapApiConfig> getConfigsByApiType(String apiType);
    
    /**
     * 根据配置代码查询配置
     * @param configCode 配置代码
     * @return 配置信息
     */
    SapApiConfig getConfigByCode(String configCode);
}
