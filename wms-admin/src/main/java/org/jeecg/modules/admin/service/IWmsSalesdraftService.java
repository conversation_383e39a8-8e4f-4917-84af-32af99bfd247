package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsSalesdraftDetail;
import org.jeecg.modules.admin.entity.WmsSalesdraft;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.vo.WmsShippingPlanBPage;
import org.jeecg.modules.admin.vo.WmsShippingPlanPage;
import org.jeecg.modules.admin.vo.WmsShippingPlanBPage;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 销售交货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsSalesdraftService extends IService<WmsSalesdraft> {

	/**
	 * 添加一对多
	 *
	 * @param wmsSalesdraft
	 * @param wmsSalesdraftDetailList
	 */
	public void saveMain(WmsSalesdraft wmsSalesdraft,List<WmsSalesdraftDetail> wmsSalesdraftDetailList) ;

	/**
	 * 修改一对多
	 *
	 * @param wmsSalesdraft
	 * @param wmsSalesdraftDetailList
	 */
	public void updateMain(WmsSalesdraft wmsSalesdraft,List<WmsSalesdraftDetail> wmsSalesdraftDetailList);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    void createFormalSalesData(String ids);

    IPage<WmsSalesdraft> querySalesdraftByItem(Page<WmsSalesdraft> page, String itemNumber, String itemName, QueryWrapper<WmsSalesdraft> queryWrapper);

    JSONObject getSendPlanStatus();

	List<WmsShippingPlanPage> getSendPlanList();
	List<WmsShippingPlanBPage> getSendPlanListForB();
}
