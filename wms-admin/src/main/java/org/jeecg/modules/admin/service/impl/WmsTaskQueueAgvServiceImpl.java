package org.jeecg.modules.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsTaskQueueAgv;
import org.jeecg.modules.admin.mapper.WmsTaskQueueAgvMapper;
import org.jeecg.modules.admin.service.IWmsTaskQueueAgvService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Service
public class WmsTaskQueueAgvServiceImpl extends ServiceImpl<WmsTaskQueueAgvMapper, WmsTaskQueueAgv> implements IWmsTaskQueueAgvService {
    @Autowired
    private WmsTaskQueueAgvMapper wmsTaskQueueAgvMapper;
    @Override
    public IPage<WmsTaskQueueAgv> pageList(Page<WmsTaskQueueAgv> page, QueryWrapper<WmsTaskQueueAgv> queryWrapper) {
        return wmsTaskQueueAgvMapper.page(page, queryWrapper);
    }
}
