package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsSpecMatchItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: 物料信息
 * @Author: jeecg-boot
 * @Date:   2024-07-15
 * @Version: V1.0
 */
public interface IWmsSpecMatchItemService extends IService<WmsSpecMatchItem> {
    //根据物料编号和账套信息判断物料是不是仓库物料
    boolean isWarehouseItem(String itemCode, String accountSetCode);

    //根据物料编号和账套信息判断物料是否免检
    boolean isExemptInspectionItem(String itemCode, String accountSetCode);

    //清除免检物料缓存
    void clearExemptInspectionCache();

    //根据物料编号和账套信息获取物料的管理方式
    String getItemManageMethod(String itemCode, String accountCode);

    //根据物料编号列表和账套信息批量获取物料的管理方式
    Map<String, String> getItemManageMethodMap(List<String> itemCodes, String accountCode);
}
