package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsSalereturnDraftDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 销售退货明细草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface WmsSalereturnDraftDetailMapper extends BaseMapper<WmsSalereturnDraftDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsSalereturnDraftDetail>
   */
	public List<WmsSalereturnDraftDetail> selectByMainId(@Param("mainId") String mainId);
}
