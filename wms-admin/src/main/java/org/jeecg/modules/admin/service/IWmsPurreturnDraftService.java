package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsPurreturnDraftDetail;
import org.jeecg.modules.admin.entity.WmsPurreturnDraft;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 采购退货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsPurreturnDraftService extends IService<WmsPurreturnDraft> {

	/**
	 * 添加一对多
	 *
	 * @param wmsPurreturnDraft
	 * @param wmsPurreturnDraftDetailList
	 */
	public void saveMain(WmsPurreturnDraft wmsPurreturnDraft,List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsPurreturnDraft
	 * @param wmsPurreturnDraftDetailList
	 */
	public void updateMain(WmsPurreturnDraft wmsPurreturnDraft,List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    void createFormalData(String ids);

    IPage<WmsPurreturnDraft> queryPurreturnDraftByItem(Page<WmsPurreturnDraft> page, String itemCode, String itemName, QueryWrapper<WmsPurreturnDraft> queryWrapper);
}
