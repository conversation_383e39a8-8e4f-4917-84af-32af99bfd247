package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsSaleReturnDetail;
import org.jeecg.modules.admin.mapper.WmsSaleReturnDetailMapper;
import org.jeecg.modules.admin.service.IWmsSaleReturnDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 销售退货明细
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Service
public class WmsSaleReturnDetailServiceImpl extends ServiceImpl<WmsSaleReturnDetailMapper, WmsSaleReturnDetail> implements IWmsSaleReturnDetailService {
	
	@Autowired
	private WmsSaleReturnDetailMapper wmsSaleReturnDetailMapper;
	
	@Override
	public List<WmsSaleReturnDetail> selectByMainId(String mainId) {
		return wmsSaleReturnDetailMapper.selectByMainId(mainId);
	}
}
