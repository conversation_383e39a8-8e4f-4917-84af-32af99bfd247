package org.jeecg.modules.admin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.SapApiConfig;
import org.jeecg.modules.admin.service.ISapApiConfigService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: SAP api配置
 * @Author: jeecg-boot
 * @Date:   2025-03-19
 * @Version: V1.0
 */
@Api(tags="SAP api配置")
@RestController
@RequestMapping("/admin/sapApiConfig")
@Slf4j
public class SapApiConfigController extends JeecgController<SapApiConfig, ISapApiConfigService> {
	@Autowired
	private ISapApiConfigService sapApiConfigService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sapApiConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "SAP api配置-分页列表查询")
	@ApiOperation(value="SAP api配置-分页列表查询", notes="SAP api配置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SapApiConfig>> queryPageList(SapApiConfig sapApiConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SapApiConfig> queryWrapper = QueryGenerator.initQueryWrapper(sapApiConfig, req.getParameterMap());
		Page<SapApiConfig> page = new Page<SapApiConfig>(pageNo, pageSize);
		IPage<SapApiConfig> pageList = sapApiConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sapApiConfig
	 * @return
	 */
	@AutoLog(value = "SAP api配置-添加")
	@ApiOperation(value="SAP api配置-添加", notes="SAP api配置-添加")
	@RequiresPermissions("admin:sap_api_config:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SapApiConfig sapApiConfig) {
		sapApiConfigService.save(sapApiConfig);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sapApiConfig
	 * @return
	 */
	@AutoLog(value = "SAP api配置-编辑")
	@ApiOperation(value="SAP api配置-编辑", notes="SAP api配置-编辑")
	@RequiresPermissions("admin:sap_api_config:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SapApiConfig sapApiConfig) {
		sapApiConfigService.updateById(sapApiConfig);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "SAP api配置-通过id删除")
	@ApiOperation(value="SAP api配置-通过id删除", notes="SAP api配置-通过id删除")
	@RequiresPermissions("admin:sap_api_config:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sapApiConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "SAP api配置-批量删除")
	@ApiOperation(value="SAP api配置-批量删除", notes="SAP api配置-批量删除")
	@RequiresPermissions("admin:sap_api_config:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sapApiConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "SAP api配置-通过id查询")
	@ApiOperation(value="SAP api配置-通过id查询", notes="SAP api配置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SapApiConfig> queryById(@RequestParam(name="id",required=true) String id) {
		SapApiConfig sapApiConfig = sapApiConfigService.getById(id);
		if(sapApiConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sapApiConfig);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sapApiConfig
    */
    @RequiresPermissions("admin:sap_api_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SapApiConfig sapApiConfig) {
        return super.exportXls(request, sapApiConfig, SapApiConfig.class, "SAP api配置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:sap_api_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SapApiConfig.class);
    }

}
