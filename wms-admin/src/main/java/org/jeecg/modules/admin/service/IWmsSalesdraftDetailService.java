package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsSalesdraftDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 销售交货草稿明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsSalesdraftDetailService extends IService<WmsSalesdraftDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsSalesdraftDetail>
	 */
	public List<WmsSalesdraftDetail> selectByMainId(String mainId);
}
