package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import org.jeecg.modules.admin.exception.SapSyncException;
import org.jeecg.modules.admin.mapper.WmsStockdReceiveSendMapper;
import org.jeecg.modules.admin.mapper.WmsStockdetailMapper;
import org.jeecg.modules.admin.service.IWmsReceiveSendHandlerService;
import org.jeecg.modules.admin.service.ISapApiService;
import org.jeecg.modules.admin.service.IErpSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class WmsReceiveSendHandlerServiceImpl implements IWmsReceiveSendHandlerService {
    @Autowired
    private ISapApiService sapApiService; // 你可以定义 ISapApiService 去对接 SAP
    @Autowired
    private WmsStockdetailMapper wmsStockdetailMapper;
    @Autowired
    private WmsStockdReceiveSendMapper wmsStockdReceiveSendMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject handleAuditData(WmsStockdReceiveSend wmsStockdReceiveSend) {
        // 记录原始审核状态和同步状态，用于确定是否需要再次处理库存
        String originalApproveStatus = wmsStockdReceiveSend.getApproveStatus();
        String originalErpSync = wmsStockdReceiveSend.getErpSync();
        
        // 使用一个标志位记录库存是否已经处理过，避免重复处理
        boolean inventoryAlreadyProcessed = false;
        
        // 添加唯一交易标识，确保重复调用时SAP能识别
        if (wmsStockdReceiveSend.getRemark2() == null || wmsStockdReceiveSend.getRemark2().isEmpty()) {
            // 生成唯一交易标识（如billNo+时间戳）作为幂等标识
            String transactionId = wmsStockdReceiveSend.getBillNo() + "_" + System.currentTimeMillis();
            wmsStockdReceiveSend.setRemark2(transactionId);
        }

        // 如果已经审核通过但同步失败，不需要再处理库存（已经处理过）
        if ("1".equals(originalApproveStatus) && 
            WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue().equals(originalErpSync)) {
            inventoryAlreadyProcessed = true;
            log.info("单据 [{}] 已审核但同步失败，跳过库存处理，仅同步SAP", wmsStockdReceiveSend.getBillNo());
        }

        // 只有在库存未处理的情况下才处理库存
        if (!inventoryAlreadyProcessed) {
            processInventory(wmsStockdReceiveSend);
        }
        
        // 同步到SAP
        return syncToSap(wmsStockdReceiveSend);
    }

    /**
     * 同步到 SAP
     */
    private JSONObject syncToSap(WmsStockdReceiveSend wmsStockdReceiveSend) {
        // 调用 SAP 接口获取同步结果
        JSONObject jsonObject = sapApiService.syncStockReceiveOrSend(wmsStockdReceiveSend);

        try {
            
            // 解析返回的 JSON 对象
            boolean result = jsonObject.getBooleanValue("result");
            String msg = jsonObject.getString("msg");

            if (result) {
                // 同步成功
                wmsStockdReceiveSend.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                wmsStockdReceiveSend.setSyncMessage(msg);  // 清除错误信息
                wmsStockdReceiveSend.setApproveStatus("1");  // 审核成功
                log.info("SAP同步成功，单据[{}]", wmsStockdReceiveSend.getBillNo());
            } else {
                // 同步失败
                wmsStockdReceiveSend.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                wmsStockdReceiveSend.setSyncMessage(msg);  // 记录 SAP 返回的错误信息
                wmsStockdReceiveSend.setApproveStatus("0");  // 审核失败
                log.error("SAP同步失败，单据[{}]，错误: {}", wmsStockdReceiveSend.getBillNo(), msg);
            }
        } catch (Exception e) {
            // 捕获其他异常（如网络超时、JSON 解析错误等）
            wmsStockdReceiveSend.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
            wmsStockdReceiveSend.setSyncMessage("同步过程中发生异常: " + e.getMessage());
            wmsStockdReceiveSend.setApproveStatus("0");  // 审核失败
            log.error("SAP同步异常，单据[{}]，错误: {}", wmsStockdReceiveSend.getBillNo(), e.getMessage());
        }
        return jsonObject;
    }

    /**
     * 根据单据类型来处理入库/出库的库存增减
     */
    private void processInventory(WmsStockdReceiveSend wmsStockdReceiveSend) {
        String billType = wmsStockdReceiveSend.getBillType();
        // 1: 入库单   2: 出库单
        // 这里和原来的逻辑类似，只不过移动到此处以保证先同步SAP，再更新库存

        String itemCode = wmsStockdReceiveSend.getItemCode();
        String batchCode = wmsStockdReceiveSend.getBatchCode();
        String warehouseCode = wmsStockdReceiveSend.getWarehouseCode();
        String accountCode = wmsStockdReceiveSend.getAccountCode();
        Double quantity = wmsStockdReceiveSend.getQuantity();

        // 必要的空值校验...
        // (省略，你也可以写在外层)

        // 查询对应库存记录
        QueryWrapper<WmsStockdetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_code", itemCode)
                .eq("warehouse_code", warehouseCode)
                .eq("batch_code", batchCode)
                .eq("account_code", accountCode)
                .eq("stock_type","PK");

        List<WmsStockdetail> stockDetails = wmsStockdetailMapper.selectList(queryWrapper);

        if ("1".equals(billType)) {
            // 收货单 -> 增加库存
            if (stockDetails != null && !stockDetails.isEmpty()) {
                // 取第一条更新
                WmsStockdetail stockDetail = stockDetails.get(0);
                stockDetail.setQuantity(stockDetail.getQuantity() + quantity);
                stockDetail.setUpdateTime(new Date());
                wmsStockdetailMapper.updateById(stockDetail);
            } else {
                // 没有记录 -> 新增
                WmsStockdetail newStock = new WmsStockdetail();
                // 初始化各种字段
                newStock.setItemCode(itemCode);
                newStock.setBatchCode(batchCode);
                newStock.setWarehouseCode(warehouseCode);
                newStock.setAccountCode(accountCode);
                newStock.setQuantity(quantity);
                newStock.setErpLock("0");
                newStock.setIsForbid("0");
                newStock.setInvState("1");
                newStock.setSplitCount(0.0);
                newStock.setMaterielType("YL");
                newStock.setLocateCode("0");
                // 设置其他字段
                newStock.setItemName(wmsStockdReceiveSend.getItemName());
                newStock.setItemSpec(wmsStockdReceiveSend.getItemSpec());
                newStock.setCreateTime(new Date());
                newStock.setStockType("PK");
                wmsStockdetailMapper.insert(newStock);
            }
        } else if ("2".equals(billType)) {
            // 发货单 -> 扣减多条库存
            // 首先对库存记录按数量从小到大排序
            if (stockDetails == null || stockDetails.isEmpty()) {
                // 如果查不到任何库存记录，说明全部扣为0（也可以直接return）
                return;
            }

            stockDetails.sort(Comparator.comparingDouble(WmsStockdetail::getQuantity));

            // 要扣的总数量
            double remainQty = quantity;

            for (WmsStockdetail sd : stockDetails) {
                if (remainQty <= 0) {
                    // 如果要扣的已经为0或小于0了，就不需要再扣
                    break;
                }

                double stockQty = sd.getQuantity();

                if (stockQty > 0) {
                    // 如果当前库存条目的数量 >= 剩余需要扣的数量
                    if (stockQty >= remainQty) {
                        // 这一条就能把剩余的扣完
                        double newQty = stockQty - remainQty;
                        sd.setQuantity(newQty);

                        if (newQty <= 0) {
                            // 如果扣到0或小于0，直接删除
                            wmsStockdetailMapper.deleteById(sd.getId());
                        } else {
                            // 否则更新剩余数量
                            sd.setUpdateTime(new Date());
                            wmsStockdetailMapper.updateById(sd);
                        }

                        // 剩余要扣的就变为0了
                        remainQty = 0;
                    } else {
                        // 当前库存条目的数量 < remainQty，先把它扣完
                        remainQty -= stockQty;
                        // 清空或删除此条记录
                        wmsStockdetailMapper.deleteById(sd.getId());
                    }
                }
            }

            // 如果最终remainQty > 0，说明还是不足，但业务不再抛异常，直接扣到没有为止
            // 可根据业务需要，做一些日志或记录
            if (remainQty > 0) {
                // 库存不够的情况，但不抛异常，只打印日志
                log.warn("库存数量不足，剩余 {} 未扣减，单据ID：{}", remainQty, wmsStockdReceiveSend.getId());
            }
        }else {
            throw new RuntimeException("未知单据类型：" + billType);
        }
    }
}
