package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.dto.WmsInventoryCountPlanDTO;
import org.jeecg.modules.admin.entity.WmsInventoryCountPlan;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 盘点方案
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface IWmsInventoryCountPlanService extends IService<WmsInventoryCountPlan> {

    void saveInventoryCountPlan(WmsInventoryCountPlanDTO dto);

    void updateInventoryCountPlan(WmsInventoryCountPlanDTO dto);

    IPage<WmsInventoryCountPlanDTO> queryPageListWithDetail(Page<WmsInventoryCountPlanDTO> page, QueryWrapper<WmsInventoryCountPlanDTO> queryWrapper);

    void executePlan(WmsInventoryCountPlanDTO dto);

    void discardPlan(String id);
}
