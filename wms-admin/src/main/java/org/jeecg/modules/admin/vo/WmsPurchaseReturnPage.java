package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsPurchaseReturn;
import org.jeecg.modules.admin.entity.WmsPurchaseReturnDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 采购退货
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_purchase_returnPage对象", description="采购退货")
public class WmsPurchaseReturnPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**SAP单据标识*/
	@Excel(name = "SAP单据标识", width = 15)
	@ApiModelProperty(value = "SAP单据标识")
    private java.lang.Integer serialNumber;
	/**采购订单号*/
	@Excel(name = "采购订单号", width = 15)
	@ApiModelProperty(value = "采购订单号")
    private java.lang.String workNo;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
	@ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15,dicCode = "purchase_return_bill_status")
	@ApiModelProperty(value = "单据状态")
	@Dict(dicCode = "purchase_return_bill_status")
	private java.lang.String billStatus;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15,dicCode = "purchase_return_bill_type")
	@ApiModelProperty(value = "单据类型")
	@Dict(dicCode = "purchase_return_bill_type")
	private java.lang.String billType;
	/**退货暂存区域*/
	@Excel(name = "退货暂存区域", width = 15)
	@ApiModelProperty(value = "退货暂存区域")
    private java.lang.String returnTempStore;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15,dicCode = "from_erp")
	@ApiModelProperty(value = "是否来自ERP")
	@Dict(dicCode = "from_erp")
	private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15,dicCode = "erp_sync")
	@ApiModelProperty(value = "ERP同步状态")
	@Dict(dicCode = "erp_sync")
	private java.lang.String erpSync;
	/**单据备注2*/
	@Excel(name = "单据备注2", width = 15)
	@ApiModelProperty(value = "单据备注2")
    private java.lang.String remark2;
	/**车牌号*/
	@Excel(name = "车牌号", width = 15)
	@ApiModelProperty(value = "车牌号")
    private java.lang.String plateNumber;
	/**快递物流信息*/
	@Excel(name = "快递物流信息", width = 15)
	@ApiModelProperty(value = "快递物流信息")
    private java.lang.String expressInfo;
	/**过账日期*/
	@Excel(name = "过账日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "过账日期")
    private java.util.Date postDate;
	/**到期日期*/
	@Excel(name = "到期日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "到期日期")
    private java.util.Date expirationDate;
	/**供应商代码*/
	@Excel(name = "供应商代码", width = 15)
	@ApiModelProperty(value = "供应商代码")
    private java.lang.String supplyCode;
	/**供应商名称*/
	@Excel(name = "供应商名称", width = 15)
	@ApiModelProperty(value = "供应商名称")
    private java.lang.String supplyName;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
	@ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
	/**预留字段1*/
	@Excel(name = "预留字段1", width = 15)
	@ApiModelProperty(value = "预留字段1")
    private java.lang.String reference1;
	/**预留字段2*/
	@Excel(name = "预留字段2", width = 15)
	@ApiModelProperty(value = "预留字段2")
    private java.lang.String reference2;
	/**预留字段3*/
	@Excel(name = "预留字段3", width = 15)
	@ApiModelProperty(value = "预留字段3")
    private java.lang.String reference3;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15)
	@ApiModelProperty(value = "账套信息")
	private java.lang.String accountCode;
	
	@ExcelCollection(name="退货明细")
	@ApiModelProperty(value = "退货明细")
	private List<WmsPurchaseReturnDetail> wmsPurchaseReturnDetailList;
	
}
