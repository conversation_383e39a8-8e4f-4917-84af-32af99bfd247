package org.jeecg.modules.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.admin.entity.WmsSendSummary;
import org.jeecg.modules.admin.entity.WmsSenddetail;
import org.jeecg.modules.admin.entity.WmsSend;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.vo.WmsInAndOutStatisticsPage;
import org.jeecg.modules.admin.vo.WmsSendDetailSummaryVO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 出库主单据
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
public interface IWmsSendService extends IService<WmsSend> {

	/**
	 * 添加一对多
	 *
	 * @param wmsSend
	 * @param wmsSenddetailList
	 */
	public void saveMain(WmsSend wmsSend,List<WmsSenddetail> wmsSenddetailList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param wmsSend
	 * @param wmsSenddetailList
	 */
	public void updateMain(WmsSend wmsSend,List<WmsSenddetail> wmsSenddetailList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	Map<String, Object> getWeekSend(String startTime, String endTime);

	Map<String, Object> getDaySend(String date);

    List<WmsInAndOutStatisticsPage> getSendReceive(String startTime, String endTime, String itemNumber, String itemName);

    void forceEndDetail(List<WmsSenddetail> wmsSenddetails);

	void generateSendSummary(String ids );

	List<WmsSenddetail> querySendDetailsForSummaryDetailId(String summaryDetailId);

    IPage<WmsSend> querySendByItem(Page<WmsSend> page, String itemNumber, String itemName,String batchCode ,QueryWrapper<WmsSend> queryWrapper);

	String getStockCirculationBillNo(String id);

	void forceEndMain(List<WmsSend> wmsSends);

	void unBindSendSummary(WmsSendSummary wmsSendSummary);

	void reverseInbound(String id, String inboundTypeStr);
	
	/**
	 * 根据多个主表ID查询子表内容并按照同品号、同批次、同仓库、同账套信息进行计划出库数量的汇总
	 *
	 * @param ids 多个主表ID，以逗号分割的字符串
	 * @return 按条件汇总后的出库明细数据
	 */
	List<WmsSendDetailSummaryVO> querySendDetailForSummary(String ids);
}
