package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 检验记录
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@ApiModel(value="wms_inspect对象", description="检验记录")
@Data
@TableName("wms_inspect")
public class WmsInspect implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**单据标识*/
	@Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNo;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**检验单号*/
	@Excel(name = "检验单号", width = 15)
    @ApiModelProperty(value = "检验单号")
    private java.lang.String workNo;
	/**供应商编号*/
	@Excel(name = "供应商编号", width = 15)
    @ApiModelProperty(value = "供应商编号")
    private java.lang.String supplyCode;
	/**供应商名称*/
	@Excel(name = "供应商名称", width = 15)
    @ApiModelProperty(value = "供应商名称")
    private java.lang.String supplyName;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "inspect_bill_type")
    @Dict(dicCode = "inspect_bill_type")
    @ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15)
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**检验员*/
	@Excel(name = "检验员", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "检验员")
    private java.lang.String inspectBy;
	/**检验日期*/
	@Excel(name = "检验日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检验日期")
    private java.util.Date inspectDate;
	/**是否来自ERP*/
	@Excel(name = "是否来自ERP", width = 15, dicCode = "from_erp")
    @Dict(dicCode = "from_erp")
    @ApiModelProperty(value = "是否来自ERP")
    private java.lang.String fromErp;
	/**ERP同步状态*/
	@Excel(name = "ERP同步状态", width = 15, dicCode = "erp_sync")
    @Dict(dicCode = "erp_sync")
    @ApiModelProperty(value = "ERP同步状态")
    private java.lang.String erpSync;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
    @ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
	/**单据备注2*/
	@Excel(name = "单据备注2", width = 15)
    @ApiModelProperty(value = "单据备注2")
    private java.lang.String remark2;
    /**账套信息*/
    @Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
}
