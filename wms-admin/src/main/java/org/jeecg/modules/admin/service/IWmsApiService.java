package org.jeecg.modules.admin.service;


import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.vo.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description: WMS接口
 * @Date: 2024-06-03 10:49
 * @Version: V1.0
 */
public interface IWmsApiService {

    //出库拣选配货
    JSONObject proOutSelect(OutSelectPage outSelectPage);
    //出库拣选发货
    JSONObject proOutSend(OutSendPage outSendPage);
    //出库取消拣选配货
    JSONObject proOutCancelSelect(OutSelectPage outSelectPage);

    JSONObject stockMark(StockMarkPage stockMarkPage);

    JSONObject stockCancel(StockMarkPage stockMarkPage);

    JSONObject stoForceOut(StoForceOutPage stoForceOutPage);

    JSONObject taskForceDone(TaskForceDonePage taskForceDonePage);

    JSONObject taskPriority(TaskPriorityPage taskPriorityPage);

    JSONObject rawOutSelect(OutSelectPage outSelectPage);

    JSONObject rawOutSend(OutSendPage outSendPage);

    JSONObject stockCheck(String itemCode, String state);

    JSONObject stockMove(StockMovePage stockMovePage);

    JSONObject rawOutBoundBack(BoundBackPage boundBackPage);
}
