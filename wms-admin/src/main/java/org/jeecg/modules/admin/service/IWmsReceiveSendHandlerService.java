package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.exception.SapSyncException;

public interface IWmsReceiveSendHandlerService {
    /**
     * 收发单审核后处理：
     * 1. 同步 SAP
     * 2. 如果成功则更新库存 + 单据状态
     * 3. 如果失败则回滚并标记单据状态为同步失败
     *
     * @param wmsStockdReceiveSend 收发单实体
     * @throws SapSyncException 同步SAP失败时抛出
     */
    JSONObject handleAuditData(WmsStockdReceiveSend wmsStockdReceiveSend);
}
