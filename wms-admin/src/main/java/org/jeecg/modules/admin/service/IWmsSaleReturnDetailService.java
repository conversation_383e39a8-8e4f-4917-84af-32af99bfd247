package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsSaleReturnDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 销售退货明细
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
public interface IWmsSaleReturnDetailService extends IService<WmsSaleReturnDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsSaleReturnDetail>
	 */
	public List<WmsSaleReturnDetail> selectByMainId(String mainId);
}
