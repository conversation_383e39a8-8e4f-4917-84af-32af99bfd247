package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsPurreturnDraftDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 采购退货草稿明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface IWmsPurreturnDraftDetailService extends IService<WmsPurreturnDraftDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<WmsPurreturnDraftDetail>
	 */
	public List<WmsPurreturnDraftDetail> selectByMainId(String mainId);
}
