package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.*;
import org.jeecg.modules.admin.mapper.WmsPurreturnDraftDetailMapper;
import org.jeecg.modules.admin.mapper.WmsPurreturnDraftMapper;
import org.jeecg.modules.admin.service.IWmsPurchaseReturnService;
import org.jeecg.modules.admin.service.IWmsPurchaseService;
import org.jeecg.modules.admin.service.IWmsPurreturnDraftService;
import org.jeecg.modules.admin.service.IWmsSpecMatchItemService;
import org.jeecg.modules.admin.mapper.WmsSpecMatchItemMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 采购退货草稿
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@Service
public class WmsPurreturnDraftServiceImpl extends ServiceImpl<WmsPurreturnDraftMapper, WmsPurreturnDraft> implements IWmsPurreturnDraftService {

	@Autowired
	private WmsPurreturnDraftMapper wmsPurreturnDraftMapper;
	@Autowired
	private WmsPurreturnDraftDetailMapper wmsPurreturnDraftDetailMapper;
	@Autowired
	private IWmsPurchaseReturnService wmsPurchaseReturnService;
	@Autowired
	private IWmsSpecMatchItemService wmsSpecMatchItemService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(WmsPurreturnDraft wmsPurreturnDraft, List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList) {
		wmsPurreturnDraftMapper.insert(wmsPurreturnDraft);
		if(wmsPurreturnDraftDetailList!=null && wmsPurreturnDraftDetailList.size()>0) {
			for(WmsPurreturnDraftDetail entity:wmsPurreturnDraftDetailList) {
				//外键设置
				entity.setWorkId(wmsPurreturnDraft.getId());
				wmsPurreturnDraftDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(WmsPurreturnDraft wmsPurreturnDraft,List<WmsPurreturnDraftDetail> wmsPurreturnDraftDetailList) {
		wmsPurreturnDraftMapper.updateById(wmsPurreturnDraft);

		//1.先删除子表数据
		wmsPurreturnDraftDetailMapper.deleteByMainId(wmsPurreturnDraft.getId());

		//2.子表数据重新插入
		if(wmsPurreturnDraftDetailList!=null && wmsPurreturnDraftDetailList.size()>0) {
			for(WmsPurreturnDraftDetail entity:wmsPurreturnDraftDetailList) {
				//外键设置
				entity.setWorkId(wmsPurreturnDraft.getId());
				wmsPurreturnDraftDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		wmsPurreturnDraftDetailMapper.deleteByMainId(id);
		wmsPurreturnDraftMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			wmsPurreturnDraftDetailMapper.deleteByMainId(id.toString());
			wmsPurreturnDraftMapper.deleteById(id);
		}
	}

	@Override
	public void createFormalData(String ids) {
		if (ids == null || ids.isEmpty()) {
			return;
		}

		Arrays.stream(ids.split(","))
				.map(wmsPurreturnDraftMapper::selectById)
				.filter(Objects::nonNull)
				.forEach(this::handleData);
	}

	// 处理采购退货草稿的数据和采购退货正式数据
	@Transactional(rollbackFor = Exception.class)
	public void handleData(WmsPurreturnDraft draft) {
		WmsPurchaseReturn purchaseReturn = buildPurchaseReturn(draft);
		List<WmsPurchaseReturnDetail> details = buildPurchaseReturnDetails(draft.getId(), purchaseReturn);

		wmsPurchaseReturnService.saveMain(purchaseReturn, details);

		// 更新草稿数据标记为正式数据
		draft.setFormalDataFlag(WmsConstant.FormalDataFlagEnum.YES.getValue());
		wmsPurreturnDraftMapper.updateById(draft);
	}

	// 创建采购退货正式数据
	private WmsPurchaseReturn buildPurchaseReturn(WmsPurreturnDraft draft) {
		String code = (String) FillRuleUtil.executeRule(FillRuleConstant.WMS_PURRETURN_ORDER_RULE, new JSONObject());
		WmsPurchaseReturn purchaseReturn = new WmsPurchaseReturn();
		purchaseReturn.setId(draft.getId());
		purchaseReturn.setSerialNumber(draft.getSerialNumber());
		purchaseReturn.setWorkNo(draft.getWorkNo());
		purchaseReturn.setBillNo(code);
		purchaseReturn.setBillName(draft.getBillName());
		purchaseReturn.setBillStatus("0");
		purchaseReturn.setBillType("CGTH");
		purchaseReturn.setFromErp(draft.getFromErp());
		purchaseReturn.setErpSync(draft.getErpSync());
		purchaseReturn.setRemark2(draft.getRemark2());
		purchaseReturn.setPostDate(draft.getPostDate());
		purchaseReturn.setExpirationDate(draft.getExpirationDate());
		purchaseReturn.setSupplyCode(draft.getSupplyCode());
		purchaseReturn.setSupplyName(draft.getSupplyName());
		purchaseReturn.setAccountCode(draft.getAccountCode());
		purchaseReturn.setRemark(draft.getRemark());
		return purchaseReturn;
	}

	// 创建采购退货正式数据明细
	private List<WmsPurchaseReturnDetail> buildPurchaseReturnDetails(String draftId, WmsPurchaseReturn purchaseReturn) {
		List<WmsPurreturnDraftDetail> draftDetails = wmsPurreturnDraftDetailMapper.selectByMainId(draftId);
		if (draftDetails == null || draftDetails.isEmpty()) {
			return Collections.emptyList();
		}

		// 获取账套信息
		String accountCode = purchaseReturn.getAccountCode();

		// 获取所有物料编码
		List<String> itemCodes = draftDetails.stream()
				.map(WmsPurreturnDraftDetail::getItemCode)
				.filter(StringUtils::isNotEmpty)
				.collect(Collectors.toList());

		// 使用Service层方法获取物料的管理方式
		Map<String, String> itemManageMethodMap = new HashMap<>();
		if (!itemCodes.isEmpty() && StringUtils.isNotEmpty(accountCode)) {
			itemManageMethodMap = wmsSpecMatchItemService.getItemManageMethodMap(itemCodes, accountCode);
		}

		Map<String, String> finalItemManageMethodMap = itemManageMethodMap;
		return draftDetails.stream().map(draftDetail -> {
			// 获取物料编码
			String itemCode = draftDetail.getItemCode();
			// 获取物料的管理方式
			String manageMethod = finalItemManageMethodMap.get(itemCode);

			// 判断 batchCode 是否为空或为 null，只有当管理方式为 Y 时才校验批次
			if ("Y".equals(manageMethod) && (draftDetail.getBatchCode() == null || draftDetail.getBatchCode().isEmpty())) {
				throw new IllegalArgumentException("批次号不能为空，对应物料编号为： " + draftDetail.getItemCode());
			}

			WmsPurchaseReturnDetail detail = new WmsPurchaseReturnDetail();
			detail.setBillNo(purchaseReturn.getBillNo());
			detail.setBillId(purchaseReturn.getId());
			detail.setItemCode(draftDetail.getItemCode());
			detail.setItemName(draftDetail.getItemName());
			detail.setItemNameShort(draftDetail.getItemNameShort());
			detail.setBatchCode(draftDetail.getBatchCode());
			detail.setItemSpec(draftDetail.getItemSpec());
			detail.setItemUnit(draftDetail.getItemUnit());
			detail.setAllQty(Optional.ofNullable(draftDetail.getAllQty()).orElse(0.0));
			detail.setPlanQty(Optional.ofNullable(draftDetail.getPlanQty()).orElse(detail.getAllQty()));
			detail.setActQty(0.0);
			detail.setLineState("0");
			detail.setRemark(draftDetail.getRemark());
			return detail;
		}).collect(Collectors.toList());
	}

	@Override
	public IPage<WmsPurreturnDraft> queryPurreturnDraftByItem(Page<WmsPurreturnDraft> page, String itemCode, String itemName, QueryWrapper<WmsPurreturnDraft> queryWrapper) {
		// 检查是否需要根据 itemCode 或 itemName 进行过滤
		if (StringUtils.isNotBlank(itemCode) || StringUtils.isNotBlank(itemName)) {
			// 构建子表的查询条件
			QueryWrapper<WmsPurreturnDraftDetail> detailQueryWrapper = new QueryWrapper<>();
			if (StringUtils.isNotBlank(itemCode)) {
				detailQueryWrapper.eq("item_code", itemCode);
			}
			if (StringUtils.isNotBlank(itemName)) {
				detailQueryWrapper.like("item_name", itemName);
			}

			// 查询符合条件的 purreturnDraftId
			List<String> purreturnDraftIds = wmsPurreturnDraftDetailMapper.selectList(detailQueryWrapper)
					.stream()
					.map(WmsPurreturnDraftDetail::getWorkId)
					.distinct()
					.collect(Collectors.toList());

			if (purreturnDraftIds.isEmpty()) {
				// 如果没有匹配的 purreturnDraftId，则返回空的分页结果
				return new Page<>();
			}

			// 将 purreturnDraftIds 作为主表的过滤条件
			queryWrapper.in("id", purreturnDraftIds);
		}

		// 执行主表的分页查询
		return this.page(page, queryWrapper);
	}
}
