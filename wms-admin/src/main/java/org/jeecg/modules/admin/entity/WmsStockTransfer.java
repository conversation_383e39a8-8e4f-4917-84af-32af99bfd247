package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 库存调拨
 * @Author: jeecg-boot
 * @Date:   2025-01-04
 * @Version: V1.0
 */
@ApiModel(value="wms_stock_transfer对象", description="库存调拨")
@Data
@TableName("wms_stock_transfer")
public class WmsStockTransfer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**调拨单号*/
	@Excel(name = "调拨单号", width = 15)
    @ApiModelProperty(value = "调拨单号")
    private java.lang.String billNo;
	/**调拨类型*/
	@Excel(name = "调拨类型", width = 15, dicCode = "wms_stock_transfer_bill_type")
    @Dict(dicCode = "wms_stock_transfer_bill_type")
    @ApiModelProperty(value = "调拨类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
    @ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**调拨时间*/
	@Excel(name = "调拨时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "调拨时间")
    private java.util.Date transferTime;
	/**调拨源仓库*/
	@Excel(name = "调拨源仓库", width = 15)
    @ApiModelProperty(value = "调拨源仓库")
    private java.lang.String sourceWarehouse;
    /**源仓库类型*/
    @Excel(name = "源仓库类型", width = 15, dicCode = "warehouse_type")
    @Dict(dicCode = "warehouse_type")
    @ApiModelProperty(value = "源仓库类型")
    private java.lang.String sourceWarehouseType;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "wms_stock_transfer_bill_status")
    @Dict(dicCode = "wms_stock_transfer_bill_status")
    @ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15)
    @ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
}
