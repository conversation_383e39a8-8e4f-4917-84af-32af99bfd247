package org.jeecg.modules.admin.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.jeecgframework.poi.excel.entity.enmus.ExcelType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.admin.entity.WmsInspectdetail;
import org.jeecg.modules.admin.entity.WmsInspect;
import org.jeecg.modules.admin.vo.WmsInspectPage;
import org.jeecg.modules.admin.service.IWmsInspectService;
import org.jeecg.modules.admin.service.IWmsInspectdetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 检验记录
 * @Author: jeecg-boot
 * @Date:   2024-09-26
 * @Version: V1.0
 */
@Api(tags="检验记录")
@RestController
@RequestMapping("/admin/wmsInspect")
@Slf4j
public class WmsInspectController {
	@Autowired
	private IWmsInspectService wmsInspectService;
	@Autowired
	private IWmsInspectdetailService wmsInspectdetailService;

	/**
	 * 分页列表查询
	 *
	 * @param wmsInspect 检验记录查询条件
	 * @param pageNo 页码
	 * @param pageSize 每页条数
	 * @param purchaseNo 采购订单号
	 * @param req 请求对象
	 * @return 分页结果
	 */
	//@AutoLog(value = "检验记录-分页列表查询")
	@ApiOperation(value="检验记录-分页列表查询", notes="检验记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WmsInspect>> queryPageList(WmsInspect wmsInspect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="purchaseNo", required=false) String purchaseNo,
								   HttpServletRequest req) {
		Page<WmsInspect> page = new Page<>(pageNo, pageSize);
		IPage<WmsInspect> pageList;

		// 判断purchaseNo是否有值
		if (purchaseNo != null && !purchaseNo.trim().isEmpty()) {
			log.info("根据采购订单号查询检验记录: {}", purchaseNo);
			// 调用服务层方法执行查询
			pageList = wmsInspectService.queryByPurchaseNo(page, wmsInspect, purchaseNo, req.getParameterMap());
		} else {
			// 原有查询逻辑
			QueryWrapper<WmsInspect> queryWrapper = QueryGenerator.initQueryWrapper(wmsInspect, req.getParameterMap());
			pageList = wmsInspectService.page(page, queryWrapper);
		}

		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param wmsInspectPage
	 * @return
	 */
	@AutoLog(value = "检验记录-添加")
	@ApiOperation(value="检验记录-添加", notes="检验记录-添加")
    @RequiresPermissions("admin:wms_inspect:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WmsInspectPage wmsInspectPage) {
		WmsInspect wmsInspect = new WmsInspect();
		BeanUtils.copyProperties(wmsInspectPage, wmsInspect);
		wmsInspectService.saveMain(wmsInspect, wmsInspectPage.getWmsInspectdetailList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param wmsInspectPage
	 * @return
	 */
	@AutoLog(value = "检验记录-编辑")
	@ApiOperation(value="检验记录-编辑", notes="检验记录-编辑")
    @RequiresPermissions("admin:wms_inspect:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WmsInspectPage wmsInspectPage) {
		WmsInspect wmsInspect = new WmsInspect();
		BeanUtils.copyProperties(wmsInspectPage, wmsInspect);
		WmsInspect wmsInspectEntity = wmsInspectService.getById(wmsInspect.getId());
		if(wmsInspectEntity==null) {
			return Result.error("未找到对应数据");
		}
		wmsInspectService.updateMain(wmsInspect, wmsInspectPage.getWmsInspectdetailList());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检验记录-通过id删除")
	@ApiOperation(value="检验记录-通过id删除", notes="检验记录-通过id删除")
    @RequiresPermissions("admin:wms_inspect:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		wmsInspectService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检验记录-批量删除")
	@ApiOperation(value="检验记录-批量删除", notes="检验记录-批量删除")
    @RequiresPermissions("admin:wms_inspect:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wmsInspectService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检验记录-通过id查询")
	@ApiOperation(value="检验记录-通过id查询", notes="检验记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WmsInspect> queryById(@RequestParam(name="id",required=true) String id) {
		WmsInspect wmsInspect = wmsInspectService.getById(id);
		if(wmsInspect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(wmsInspect);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检验明细-通过主表ID查询")
	@ApiOperation(value="检验明细-通过主表ID查询", notes="检验明细-通过主表ID查询")
	@GetMapping(value = "/queryWmsInspectdetailByMainId")
	public Result<IPage<WmsInspectdetail>> queryWmsInspectdetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<WmsInspectdetail> wmsInspectdetailList = wmsInspectdetailService.selectByMainId(id);
		IPage <WmsInspectdetail> page = new Page<>();
		page.setRecords(wmsInspectdetailList);
		page.setTotal(wmsInspectdetailList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param wmsInspect
    */
    @RequiresPermissions("admin:wms_inspect:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WmsInspect wmsInspect) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<WmsInspect> queryWrapper = QueryGenerator.initQueryWrapper(wmsInspect, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      List<String> selectionList = null;
      if(oConvertUtils.isNotEmpty(selections)) {
           selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }

      //Step.2 获取导出数据
      List<WmsInspect> wmsInspectList = wmsInspectService.list(queryWrapper);
      if(wmsInspectList == null || wmsInspectList.isEmpty()) {
          // 如果没有数据，直接返回空的Excel
          ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
          mv.addObject(NormalExcelConstants.FILE_NAME, "检验记录列表");
          mv.addObject(NormalExcelConstants.CLASS, WmsInspectPage.class);
          ExportParams exportParams = new ExportParams("检验记录数据", "导出人:"+sysUser.getRealname(), "检验记录");
          // 设置Excel类型为XSSF (xlsx格式，更快)
          exportParams.setType(ExcelType.XSSF);
          mv.addObject(NormalExcelConstants.PARAMS, exportParams);
          mv.addObject(NormalExcelConstants.DATA_LIST, new ArrayList<>());
          return mv;
      }

      // 获取所有主表ID
      List<String> mainIds = wmsInspectList.stream().map(WmsInspect::getId).collect(Collectors.toList());

      // 批量查询所有子表数据
      Map<String, List<WmsInspectdetail>> detailsMap = new HashMap<>();
      if(!mainIds.isEmpty()) {
          // 构建查询条件
          QueryWrapper<WmsInspectdetail> detailQueryWrapper = new QueryWrapper<>();
          detailQueryWrapper.in("bill_id", mainIds);
          // 一次性查询所有子表数据
          List<WmsInspectdetail> allDetails = wmsInspectdetailService.list(detailQueryWrapper);

          // 按主表ID分组
          detailsMap = allDetails.stream().collect(Collectors.groupingBy(WmsInspectdetail::getBillId));
      }

      // Step.3 组装pageList
      List<WmsInspectPage> pageList = new ArrayList<WmsInspectPage>();
      for (WmsInspect main : wmsInspectList) {
          WmsInspectPage vo = new WmsInspectPage();
          BeanUtils.copyProperties(main, vo);
          // 从Map中获取对应的子表数据，避免循环查询数据库
          List<WmsInspectdetail> wmsInspectdetailList = detailsMap.getOrDefault(main.getId(), Collections.emptyList());
          vo.setWmsInspectdetailList(wmsInspectdetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "检验记录列表");
      mv.addObject(NormalExcelConstants.CLASS, WmsInspectPage.class);

      // 创建并配置ExportParams，优化导出性能
      ExportParams exportParams = new ExportParams("检验记录数据", "导出人:"+sysUser.getRealname(), "检验记录");
      // 设置Excel类型为XSSF (xlsx格式，更快)
      exportParams.setType(ExcelType.XSSF);
      // 设置冻结列和行，提高大数据量时的查看体验
      exportParams.setFreezeCol(2);

      mv.addObject(NormalExcelConstants.PARAMS, exportParams);
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:wms_inspect:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<WmsInspectPage> list = ExcelImportUtil.importExcel(file.getInputStream(), WmsInspectPage.class, params);
              for (WmsInspectPage page : list) {
                  WmsInspect po = new WmsInspect();
                  BeanUtils.copyProperties(page, po);
                  wmsInspectService.saveMain(po, page.getWmsInspectdetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

	 /**
	  * 根据工单编号查询检验记录明细
	  * @param workNo
	  * @return
	  */
	 @ApiOperation(value="根据工单编号查询检验记录明细", notes="根据工单编号查询检验记录明细")
	 @GetMapping(value = "/queryInspectDetailByWorkNo")
	 public Result<List<WmsInspectdetail>> queryInspectDetailByWorkNo(@RequestParam(name="workNo",required=true) String workNo) {
		 List<WmsInspectdetail> wmsInspectdetailList = wmsInspectdetailService.selectByWorkNo(workNo);
		 return Result.OK(wmsInspectdetailList);
	 }

}
