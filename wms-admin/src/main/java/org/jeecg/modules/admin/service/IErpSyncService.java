package org.jeecg.modules.admin.service;

/**
 * ERP 同步状态处理
 * 可被多个业务复用，如库存收发、库存转储等
 */
public interface IErpSyncService {

    /**
     * 通用更新某业务单据的审核状态与同步状态
     * @param id 主键ID
     * @param approveStatus 审核状态（如"0"=未审核，"1"=已审核）
     * @param erpSync 同步状态（如"SYNC_SUCCESS", "SYNC_FAIL"...）
     */
    void updateStockdReceiveSendErpSyncStatus(String id, String approveStatus, String erpSync);

    /**
     * 更新 erpSync 状态为同步成功
     *
     * @param stockDumpId 库存转储单 ID
     */
    void updateErpSyncSuccess(String stockDumpId);

    /**
     * 更新 erpSync 状态为同步失败
     *
     * @param stockDumpId 库存转储单 ID
     */
    void updateErpSyncFail(String stockDumpId);

}
