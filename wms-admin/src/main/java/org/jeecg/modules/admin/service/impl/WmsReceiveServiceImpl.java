package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsFeatureConfiguration;
import org.jeecg.modules.admin.entity.WmsPurchaseDetail;
import org.jeecg.modules.admin.entity.WmsPurchase;
import org.jeecg.modules.admin.entity.WmsReceive;
import org.jeecg.modules.admin.entity.WmsReceivedetail;
import org.jeecg.modules.admin.entity.WmsSpecMatchItem;
import org.jeecg.modules.admin.entity.WmsStockdetail;
import org.jeecg.modules.admin.entity.SapApiConfig;
import org.jeecg.modules.admin.mapper.WmsReceivedetailMapper;
import org.jeecg.modules.admin.mapper.WmsReceiveMapper;
import org.jeecg.modules.admin.mapper.WmsStockdetailMapper;
import org.jeecg.modules.admin.service.*;
import org.jeecg.modules.admin.util.DateUtils;
import org.jeecg.modules.admin.util.SapApiUtil;
import org.jeecg.modules.admin.vo.WmsRecevieAndDetailPage;
import org.jeecg.modules.base.service.BaseCommonService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * @Description: 收货主单据
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class WmsReceiveServiceImpl extends ServiceImpl<WmsReceiveMapper, WmsReceive> implements IWmsReceiveService {

    @Autowired
    private WmsReceiveMapper wmsReceiveMapper;
    @Autowired
    private WmsReceivedetailMapper wmsReceivedetailMapper;
    @Autowired
    private IWmsPurchaseService purchaseService;
    @Autowired
    private IWmsPurchaseDetailService purchaseDetailService;
    @Autowired
    private IWmsSpecMatchItemService specMatchItemService;
    @Autowired
    private WmsStockdetailMapper wmsStockdetailMapper;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ISapApiConfigService sapApiConfigService;
    @Autowired
    private IWmsFeatureConfigurationService wmsFeatureConfigurationService;
    @Autowired
    private IWmsInspectService wmsInspectService;
    @Autowired
    private IWmsInspectdetailService wmsInspectDetailService;

    @Value("${sap.api.main-base-url}")
    private String mainBaseUrl;

    // 采购送检单接口的配置代码
    private static final String PURCHASE_INSPECTION_CONFIG_CODE = "POST_PURCHASE_INSPECTION";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList) {
        // 插入主表数据
        wmsReceiveMapper.insert(wmsReceive);

        if (wmsReceivedetailList != null && !wmsReceivedetailList.isEmpty()) {
            String billNo = wmsReceive.getBillNo();
            String barcodePrefix = billNo;

            // 获取已有的 itemBarcode 列表，减少数据库查询次数
            List<WmsReceivedetail> existingDetails = wmsReceivedetailMapper.selectList(
                    new QueryWrapper<WmsReceivedetail>().likeRight("item_barcode", barcodePrefix)
            );

            Set<String> existingItemBarcodes = existingDetails.stream()
                    .map(WmsReceivedetail::getItemBarcode)
                    .collect(Collectors.toSet());

            // 计算当前最大序号
            int maxSequenceNumber = existingItemBarcodes.stream()
                    .map(barcode -> barcode.substring(barcodePrefix.length()))
                    .filter(sequenceStr -> sequenceStr.matches("\\d+"))
                    .mapToInt(Integer::parseInt)
                    .max()
                    .orElse(0);

            int sequenceNumber = maxSequenceNumber + 1;
            Set<String> generatedItemBarcodes = new HashSet<>();

            for (WmsReceivedetail entity : wmsReceivedetailList) {
                // 设置外键
                entity.setBillId(wmsReceive.getId());

                // 判断 itemBarcode 是否有值
                if (entity.getItemBarcode() == null || entity.getItemBarcode().isEmpty()) {
                    String itemBarcode;

                    do {
                        itemBarcode = billNo + String.format("%03d", sequenceNumber++);
                    } while (existingItemBarcodes.contains(itemBarcode) || generatedItemBarcodes.contains(itemBarcode));

                    // 设置唯一的 itemBarcode
                    entity.setItemBarcode(itemBarcode);
                    generatedItemBarcodes.add(itemBarcode);
                }
                wmsReceivedetailMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList) {
        if (wmsReceive.getFromErp().equals(WmsConstant.FromErpEnum.YES.getValue()) && wmsReceive.getBillType().equals(WmsConstant.TaskTypeEnum.CGRK.getValue())){
            // 获取原有的明细数据
            List<WmsReceivedetail> oldDetailList = wmsReceivedetailMapper.selectByMainId(wmsReceive.getId());
            // 删除相关的库存记录
            deleteRelatedStockByDetails(oldDetailList, wmsReceivedetailList);
        }

        // 更新主表数据
        wmsReceiveMapper.updateById(wmsReceive);

        // 1. 先删除子表数据
        wmsReceivedetailMapper.deleteByMainId(wmsReceive.getId());

        // 2. 子表数据重新插入
        if (wmsReceivedetailList != null && !wmsReceivedetailList.isEmpty()) {
            // 获取最大条码的顺序号
            int maxSequenceNumber = 0;
            String billNo = wmsReceive.getBillNo();

            for (WmsReceivedetail entity : wmsReceivedetailList) {
                String itemBarcode = entity.getItemBarcode();
                if (StringUtils.isNotBlank(itemBarcode)) {
                    if (itemBarcode.length() > billNo.length()) {
                        String sequencePart = itemBarcode.substring(billNo.length());
                        try {
                            int sequenceNumber = Integer.parseInt(sequencePart);
                            maxSequenceNumber = Math.max(maxSequenceNumber, sequenceNumber);
                        } catch (NumberFormatException e) {
                            // 记录日志或处理条码格式不正确的情况
                            System.err.println("条码顺序号部分格式不正确: " + itemBarcode);
                            // 根据需求决定是否抛出异常或继续处理
                        }
                    } else {
                        // 记录日志或处理条码长度不足的情况
                        System.err.println("条码长度小于单据号长度: " + itemBarcode);
                        // 根据需求决定是否抛出异常或继续处理
                    }
                }
            }

            // 顺序号从最大序号 + 1 开始
            int sequenceNumber = maxSequenceNumber + 1;

            for (WmsReceivedetail entity : wmsReceivedetailList) {
                // 设置外键
                entity.setBillId(wmsReceive.getId());

                // 检查 itemBarcode 是否为空
                if (StringUtils.isBlank(entity.getItemBarcode())) {
                    // 生成新的 itemBarcode
                    String newItemBarcode = generateItemBarcode(billNo, sequenceNumber);
                    entity.setItemBarcode(newItemBarcode);

                    // 顺序号递增
                    sequenceNumber++;
                }
                  // 插入子表数据
                wmsReceivedetailMapper.insert(entity);
            }
        }
    }


    /**
     * 根据收货明细删除对应的库存记录
     * @param oldDetailList 原收货明细列表
     * @param newDetailList 新收货明细列表
     */
    private void deleteRelatedStockByDetails(List<WmsReceivedetail> oldDetailList, List<WmsReceivedetail> newDetailList) {
        if (oldDetailList == null || oldDetailList.isEmpty()) {
            return;
        }

        // 获取新列表中的ID集合
        Set<String> newDetailIds = newDetailList == null ? Collections.emptySet() :
                newDetailList.stream()
                        .map(WmsReceivedetail::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

        // 找出在旧列表中存在但在新列表中不存在的记录
        List<WmsReceivedetail> deletedDetails = oldDetailList.stream()
                .filter(detail -> !newDetailIds.contains(detail.getId()))
                .collect(Collectors.toList());

        // 删除对应的库存记录
        for (WmsReceivedetail detail : deletedDetails) {
            QueryWrapper<WmsStockdetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("bill_dtl_id", detail.getId())
                            .eq("stock_type",WmsConstant.StockTypeEnum.PK.getValue())
                                    .ne("inv_state",WmsConstant.InvStateEnum.ALLOCATED.getValue());
            wmsStockdetailMapper.delete(queryWrapper);
        }
    }



    /**
     * 生成新的物料条码
     *
     * @param billNo         单据号
     * @param sequenceNumber 顺序号
     * @return 生成的物料条码
     */
    private String generateItemBarcode(String billNo, int sequenceNumber) {
        return billNo + String.format("%03d", sequenceNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        wmsReceivedetailMapper.deleteByMainId(id);
        wmsReceiveMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            wmsReceivedetailMapper.deleteByMainId(id.toString());
            wmsReceiveMapper.deleteById(id);
        }
    }

    @Override
    public Map<String, Object> getWeekReceive(String startTime, String endTime) {
        // 解析开始时间和结束时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime, formatter);
        LocalDate endDate = LocalDate.parse(endTime, formatter);

        // 获取指定时间范围内的收货单据明细
        QueryWrapper<WmsReceivedetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("DATE(create_time)", startDate, endDate);
        List<WmsReceivedetail> receiveList = wmsReceivedetailMapper.selectList(queryWrapper);

        // 统计时间范围内每天的收货数量
        Map<String, Double> dailyTotals = receiveList.stream()
                .collect(Collectors.groupingBy(
                        receive -> receive.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(formatter),
                        Collectors.summingDouble(WmsReceivedetail::getStandardQty)
                ));

        // 获取时间范围内的日期列表
        long numOfDays = endDate.toEpochDay() - startDate.toEpochDay() + 1;
        List<String> dateList = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(numOfDays)
                .map(date -> date.format(formatter))
                .collect(Collectors.toList());

        // 收货数量列表
        List<String> numList = dateList.stream()
                .map(date -> dailyTotals.getOrDefault(date, 0.0).toString())
                .collect(Collectors.toList());

        // 构建结果 Map
        Map<String, Object> result = new HashMap<>();
        result.put("Num", numList);
        result.put("dayList", dateList);

        return result;
    }

    @Override
    public Map<String, Object> getDayReceive(String date) {
        // 初始化每小时的入库数量为0
        Map<String, Double> hourlyTotals = IntStream.range(0, 24)
                .boxed()
                .collect(Collectors.toMap(hour -> String.format("%02d:00", hour), hour -> 0.0));

        // 获取指定日期的收货单据明细
        QueryWrapper<WmsReceivedetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("DATE(create_time) = {0}", date);
        List<WmsReceivedetail> receiveList = wmsReceivedetailMapper.selectList(queryWrapper);

        // 统计每小时的入库数量
        receiveList.forEach(receive -> {
            String hour = String.format("%02d:00", receive.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).getHour());
            hourlyTotals.put(hour, hourlyTotals.get(hour) + receive.getStandardQty());
        });

        // 获取小时列表
        List<String> hourList = IntStream.range(0, 24)
                .boxed()
                .map(hour -> String.format("%02d:00", hour))
                .collect(Collectors.toList());

        // 获取数量列表
        List<String> numList = hourList.stream()
                .map(hour -> hourlyTotals.getOrDefault(hour, 0.0).toString())
                .collect(Collectors.toList());

        // 构建结果 Map
        Map<String, Object> result = new HashMap<>();
        result.put("Num", numList);
        result.put("hourList", hourList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseReceive(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList) {
        // 判断收货日期是否为空，为空则设置为当前时间
        if (wmsReceive.getReceiveDate() == null) {
            wmsReceive.setReceiveDate(new Date());
        }
        wmsReceive.setBillType("CGRK");

        // 检查是否已有对应的收货记录
        WmsReceive receive = super.getById(wmsReceive.getId());

        if (receive == null) {
            // 新增主表和明细表，并将主表的 erp_sync 设置为 0
            this.saveMain(wmsReceive, wmsReceivedetailList);

            // 为每条明细生成平库库存
            for (WmsReceivedetail detail : wmsReceivedetailList) {
                // 查询已保存的明细记录，获取生成的ID
                QueryWrapper<WmsReceivedetail> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("bill_id", wmsReceive.getId())
                        .eq("item_number", detail.getItemNumber())
                        .eq("line_no", detail.getLineNo());
                WmsReceivedetail savedDetail = wmsReceivedetailMapper.selectOne(queryWrapper);

                if (savedDetail != null) {
                    // 根据品号查询物料信息，判断是否免检
                    WmsSpecMatchItem specMatchItem = getSpecMatchItemByItemNumber(savedDetail.getItemNumber(), wmsReceive.getAccountCode());
                    boolean isExemptInspection = false;
                    if (specMatchItem != null) {
                        isExemptInspection = "1".equals(specMatchItem.getFreeFlag());
                    }

                    // 生成平库库存，传递免检标志
                    createPlatformInventory(savedDetail, wmsReceive, isExemptInspection);
                }
            }
        } else {
            // 更新现有记录时，也将主表的 erp_sync 设置为 0
            wmsReceive.setErpSync("0");
            wmsReceive.setBillNo(receive.getBillNo());
            this.updateById(wmsReceive); // 更新主表的 erp_sync 字段

            // 获取已存在的 itemBarcode 列表，减少数据库查询次数
            String billNo = receive.getBillNo();
            String barcodePrefix = billNo;

            // 查询当前单据的所有明细，获取已有的 itemBarcode
            List<WmsReceivedetail> existingDetails = wmsReceivedetailMapper.selectList(
                    new QueryWrapper<WmsReceivedetail>().eq("bill_id", receive.getId())
            );

            Set<String> existingItemBarcodes = existingDetails.stream()
                    .map(WmsReceivedetail::getItemBarcode)
                    .filter(Objects::nonNull)
                    .filter(barcode -> !barcode.isEmpty())
                    .collect(Collectors.toSet());

            // 计算当前最大序号
            int maxSequenceNumber = existingItemBarcodes.stream()
                    .filter(barcode -> barcode.startsWith(barcodePrefix))
                    .filter(barcode -> barcode.length() > barcodePrefix.length())
                    .map(barcode -> barcode.substring(barcodePrefix.length()))
                    .filter(sequenceStr -> sequenceStr.matches("\\d+"))
                    .mapToInt(Integer::parseInt)
                    .max()
                    .orElse(0);

            int sequenceNumber = maxSequenceNumber + 1;
            Set<String> generatedItemBarcodes = new HashSet<>();

            // 处理新增的明细信息
            for (WmsReceivedetail detail : wmsReceivedetailList) {
                detail.setBillId(wmsReceive.getId());
                detail.setReceiveTag(WmsConstant.ReceiveTagEnum.NO.getValue());
                detail.setBillNo(receive.getBillNo());

                // 根据品号查询物料信息
                WmsSpecMatchItem specMatchItem = getSpecMatchItemByItemNumber(detail.getItemNumber(), wmsReceive.getAccountCode());
                if (specMatchItem != null) {
                    // 根据免检标记设置检验标记
                    String freeFlag = specMatchItem.getFreeFlag();
                    detail.setCheckTag("1".equals(freeFlag) ? "3" : "0");
                } else {
                    // 如果物料信息不存在，设置检验标记为未检验
                    detail.setCheckTag("0");
                }

                // 判断 itemBarcode 是否有值，如果没有则生成新的唯一编号
                if (detail.getItemBarcode() == null || detail.getItemBarcode().isEmpty()) {
                    String itemBarcode;
                    do {
                        itemBarcode = billNo + String.format("%03d", sequenceNumber++);
                    } while (existingItemBarcodes.contains(itemBarcode) || generatedItemBarcodes.contains(itemBarcode));

                    detail.setItemBarcode(itemBarcode);
                    generatedItemBarcodes.add(itemBarcode);
                }

                // 插入明细记录
                wmsReceivedetailMapper.insert(detail);
            }
        }

        // 统一处理已打印数量的更新
        for (WmsReceivedetail detail : wmsReceivedetailList) {
            QueryWrapper<WmsPurchaseDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("work_no", wmsReceive.getWorkNo())
                    .eq("item_number", detail.getItemNumber())
                    .eq("line_no", detail.getLineNo());
            WmsPurchaseDetail purchaseDetail = purchaseDetailService.getOne(queryWrapper);
            if (purchaseDetail != null) {
                purchaseDetail.setPrintedQty(purchaseDetail.getPrintedQty() + detail.getBatchQty());
                purchaseDetail.setPrintedTime(new Date());
                purchaseDetail.setPrintedNumber(purchaseDetail.getPrintedNumber() + 1);
                purchaseDetailService.updateById(purchaseDetail);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseInbound(WmsReceive wmsReceive, List<WmsReceivedetail> wmsReceivedetailList) {
        wmsReceive.setBillType("CGRK");

        // 检查是否已有对应的收货记录
        WmsReceive receive = super.getById(wmsReceive.getId());

        // 用于收集需要送检的明细
        List<WmsReceivedetail> inspectionDetailsList = new ArrayList<>();

        if (receive == null) {
            // 处理明细信息
            for (WmsReceivedetail detail : wmsReceivedetailList) {
                detail.setReceiveTag(WmsConstant.ReceiveTagEnum.NO.getValue());

                // 确保仓库编号已设置
                if (detail.getWarehouseCode() == null || detail.getWarehouseCode().isEmpty()) {
                    throw new RuntimeException("仓库编号不能为空");
                }

                // 根据品号查询物料信息
                WmsSpecMatchItem specMatchItem = getSpecMatchItemByItemNumber(detail.getItemNumber(), wmsReceive.getAccountCode());
                boolean isExemptInspection = false;

                if (specMatchItem != null) {
                    // 根据免检标记设置检验标记
                    String freeFlag = specMatchItem.getFreeFlag();
                    isExemptInspection = "1".equals(freeFlag);

                    if (isExemptInspection) {
                        // 免检物料
                        detail.setCheckTag(WmsConstant.CheckTagEnum.NO_CHECK.getValue()); // 3-免检
                        // 免检物料的合格数量等于批号数量
                        detail.setStandardQty(detail.getBatchQty());
                    } else {
                        // 非免检物料
                        detail.setCheckTag(WmsConstant.CheckTagEnum.NO.getValue()); // 0-未检验
                        // 非免检物料的合格数量为0
                        detail.setStandardQty(0.0);
                        // 收集需要送检的明细
                        inspectionDetailsList.add(detail);
                    }
                } else {
                    // 如果物料信息不存在，设置检验标记为未检验
                    detail.setCheckTag(WmsConstant.CheckTagEnum.NO.getValue()); // 0-未检验
                    // 非免检物料的合格数量为0
                    detail.setStandardQty(0.0);
                    // 收集需要送检的明细
                    inspectionDetailsList.add(detail);
                }

                // 设置入库状态为入库完成
                detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue()); // 5-入库完成

                // 设置入库数量
                if (detail.getInboundQty() == null) {
                    detail.setInboundQty(detail.getBatchQty());
                }
            }

            // 处理完成后调用saveMain方法保存数据
            this.saveMain(wmsReceive, wmsReceivedetailList);

            // 为每条明细生成平库库存
            for (WmsReceivedetail detail : wmsReceivedetailList) {
                // 查询已保存的明细记录，获取生成的ID
                QueryWrapper<WmsReceivedetail> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("bill_id", wmsReceive.getId())
                        .eq("item_number", detail.getItemNumber())
                        .eq("line_no", detail.getLineNo());
                WmsReceivedetail savedDetail = wmsReceivedetailMapper.selectOne(queryWrapper);

                if (savedDetail != null) {
                    // 根据品号查询物料信息，判断是否免检
                    WmsSpecMatchItem specMatchItem = getSpecMatchItemByItemNumber(savedDetail.getItemNumber(), wmsReceive.getAccountCode());
                    boolean isExemptInspection = false;
                    if (specMatchItem != null) {
                        isExemptInspection = "1".equals(specMatchItem.getFreeFlag());
                    }

                    // 生成平库库存，但不发送送检请求
                    createPlatformInventory(savedDetail, wmsReceive, isExemptInspection);
                }
            }
        } else {
            // 更新现有记录时，也将主表的 erp_sync 设置为 0
            wmsReceive.setErpSync("0");
            wmsReceive.setBillNo(receive.getBillNo());
            this.updateById(wmsReceive); // 更新主表的 erp_sync 字段

            // 获取已存在的 itemBarcode 列表，减少数据库查询次数
            String billNo = receive.getBillNo();
            String barcodePrefix = billNo;

            // 查询当前单据的所有明细，获取已有的 itemBarcode
            List<WmsReceivedetail> existingDetails = wmsReceivedetailMapper.selectList(
                    new QueryWrapper<WmsReceivedetail>().eq("bill_id", receive.getId())
            );

            Set<String> existingItemBarcodes = existingDetails.stream()
                    .map(WmsReceivedetail::getItemBarcode)
                    .filter(Objects::nonNull)
                    .filter(barcode -> !barcode.isEmpty())
                    .collect(Collectors.toSet());

            // 计算当前最大序号
            int maxSequenceNumber = existingItemBarcodes.stream()
                    .filter(barcode -> barcode.startsWith(barcodePrefix))
                    .filter(barcode -> barcode.length() > barcodePrefix.length())
                    .map(barcode -> barcode.substring(barcodePrefix.length()))
                    .filter(sequenceStr -> sequenceStr.matches("\\d+"))
                    .mapToInt(Integer::parseInt)
                    .max()
                    .orElse(0);

            int sequenceNumber = maxSequenceNumber + 1;
            Set<String> generatedItemBarcodes = new HashSet<>();

            // 处理新增的明细信息
            for (WmsReceivedetail detail : wmsReceivedetailList) {
                detail.setBillId(wmsReceive.getId());
                detail.setReceiveTag(WmsConstant.ReceiveTagEnum.NO.getValue());
                detail.setBillNo(receive.getBillNo());

                // 确保仓库编号已设置
                if (detail.getWarehouseCode() == null || detail.getWarehouseCode().isEmpty()) {
                    throw new RuntimeException("仓库编号不能为空");
                }

                // 根据品号查询物料信息
                WmsSpecMatchItem specMatchItem = getSpecMatchItemByItemNumber(detail.getItemNumber(), wmsReceive.getAccountCode());
                boolean isExemptInspection = false;

                if (specMatchItem != null) {
                    // 根据免检标记设置检验标记
                    String freeFlag = specMatchItem.getFreeFlag();
                    isExemptInspection = "1".equals(freeFlag);

                    if (isExemptInspection) {
                        // 免检物料
                        detail.setCheckTag(WmsConstant.CheckTagEnum.NO_CHECK.getValue()); // 3-免检
                        // 免检物料的合格数量等于批号数量
                        detail.setStandardQty(detail.getBatchQty());
                    } else {
                        // 非免检物料
                        detail.setCheckTag(WmsConstant.CheckTagEnum.NO.getValue()); // 0-未检验
                        // 非免检物料的合格数量为0
                        detail.setStandardQty(0.0);
                        // 收集需要送检的明细
                        inspectionDetailsList.add(detail);
                    }
                } else {
                    // 如果物料信息不存在，设置检验标记为未检验
                    detail.setCheckTag(WmsConstant.CheckTagEnum.NO.getValue()); // 0-未检验
                    // 非免检物料的合格数量为0
                    detail.setStandardQty(0.0);
                    // 收集需要送检的明细
                    inspectionDetailsList.add(detail);
                }

                // 判断 itemBarcode 是否有值，如果没有则生成新的唯一编号
                if (detail.getItemBarcode() == null || detail.getItemBarcode().isEmpty()) {
                    String itemBarcode;
                    do {
                        itemBarcode = billNo + String.format("%03d", sequenceNumber++);
                    } while (existingItemBarcodes.contains(itemBarcode) || generatedItemBarcodes.contains(itemBarcode));

                    detail.setItemBarcode(itemBarcode);
                    generatedItemBarcodes.add(itemBarcode);
                }

                // 设置入库状态为入库完成
                detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue()); // 5-入库完成

                // 设置入库数量
                if (detail.getInboundQty() == null) {
                    detail.setInboundQty(detail.getBatchQty());
                }

                // 插入明细记录
                wmsReceivedetailMapper.insert(detail);

                // 生成平库库存，但不发送送检请求
                createPlatformInventory(detail, wmsReceive, isExemptInspection);
            }
        }

        // 统一处理已打印数量的更新
        for (WmsReceivedetail detail : wmsReceivedetailList) {
            QueryWrapper<WmsPurchaseDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("work_no", wmsReceive.getWorkNo())
                    .eq("item_number", detail.getItemNumber())
                    .eq("line_no", detail.getLineNo())
                    .last("LIMIT 1");
            WmsPurchaseDetail purchaseDetail = purchaseDetailService.getOne(queryWrapper);
            if (purchaseDetail != null) {
                purchaseDetail.setPrintedQty(purchaseDetail.getPrintedQty() + detail.getBatchQty());
                purchaseDetail.setPrintedTime(new Date());
                purchaseDetail.setPrintedNumber(purchaseDetail.getPrintedNumber() + 1);
                purchaseDetailService.updateById(purchaseDetail);
            }
        }

        // 如果有需要送检的明细，批量发送送检请求
        if (!inspectionDetailsList.isEmpty()) {
            try {
                String result = sendBatchPurchaseInspectionToSAP(inspectionDetailsList, wmsReceive);
                if (result != null) {
                    // 如果返回了错误信息，记录日志
                    log.error("批量发送采购送检申请失败，错误信息：{}", result);
                }
            } catch (Exception e) {
                // 捕获异常但不影响主流程，记录错误日志
                log.error("批量发送采购送检申请时发生异常", e);
                // 更新所有需要送检明细的检验标记为"送检失败"(4)
                for (WmsReceivedetail detail : inspectionDetailsList) {
                    detail.setCheckTag(WmsConstant.CheckTagEnum.CHECK_FAIL.getValue());
                    wmsReceivedetailMapper.updateById(detail);
                }
            }
        }
    }

    /**
     * 创建平库库存记录
     * @param detail 收货明细
     * @param receive 收货主单据
     * @param isExemptInspection 是否免检
     */
    private void createPlatformInventory(WmsReceivedetail detail, WmsReceive receive, boolean isExemptInspection) {
        if (isExemptInspection) {
            // 免检物料 - 保持原有逻辑（存在则更新，不存在则新增）
            // 查询对应库存记录
            QueryWrapper<WmsStockdetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("item_code", detail.getItemNumber())
                    .eq("warehouse_code", detail.getWarehouseCode())
                    .eq("batch_code", detail.getBatchCode())
                    .eq("account_code", receive.getAccountCode())
                    .eq("stock_type", "PK");

            List<WmsStockdetail> stockDetails = wmsStockdetailMapper.selectList(queryWrapper);

            if (stockDetails != null && !stockDetails.isEmpty()) {
                // 存在库存记录，更新第一条
                WmsStockdetail stockDetail = stockDetails.get(0);
                stockDetail.setQuantity(stockDetail.getQuantity() + detail.getInboundQty());
                stockDetail.setIsForbid("0"); // 0-正常
                stockDetail.setUpdateTime(new Date());
                wmsStockdetailMapper.updateById(stockDetail);
            } else {
                // 不存在库存记录，新增
                WmsStockdetail newStock = createNewStockDetail(detail, receive, true);
                wmsStockdetailMapper.insert(newStock);
            }
        } else {
            // 非免检物料 - 总是创建新的库存记录（状态为不可用）
            WmsStockdetail newStock = createNewStockDetail(detail, receive, false);
            wmsStockdetailMapper.insert(newStock);

            // 不再在此处发送送检请求，而是收集起来批量发送
        }
    }

    /**
     * 创建新的库存记录
     * @param detail 收货明细
     * @param receive 收货主单据
     * @param isExemptInspection 是否免检
     * @return 新的库存记录
     */
    private WmsStockdetail createNewStockDetail(WmsReceivedetail detail, WmsReceive receive, boolean isExemptInspection) {
        WmsStockdetail newStock = new WmsStockdetail();
        // 初始化各种字段
        newStock.setItemCode(detail.getItemNumber());
        newStock.setBatchCode(detail.getBatchCode());
        newStock.setItemBarcode(detail.getItemBarcode());
        newStock.setWarehouseCode(detail.getWarehouseCode());
        newStock.setAccountCode(receive.getAccountCode());
        newStock.setQuantity(detail.getInboundQty());
        newStock.setErpLock("0");
        newStock.setInvState(WmsConstant.InvStateEnum.NORMAL.getValue());

        // 根据是否免检设置库存状态
        if (isExemptInspection) {
            newStock.setIsForbid("0");//0-正常
        } else {
            newStock.setIsForbid("1"); // 1-不可用
        }

        newStock.setSplitCount(0.0);
        newStock.setMaterielType("YL");
        newStock.setLocateCode("0");
        // 设置其他字段
        newStock.setItemName(detail.getItemName());
        newStock.setItemSpec(detail.getItemSpec());
        newStock.setCreateTime(new Date());
        newStock.setStockType("PK");
        newStock.setBillDtlId(detail.getId());

        // 如果有生产日期和过期日期，也设置上
        if (detail.getProductDate() != null) {
            newStock.setProductDate(detail.getProductDate());
        }
        if (detail.getExpirationDate() != null) {
            newStock.setExpirationDate(detail.getExpirationDate());
        }

        return newStock;
    }

    /**
     * 批量发送采购送检请求到SAP
     * @param detailsList 收货明细列表
     * @param receive 收货主单据
     * @return 处理结果信息，成功返回null，失败返回错误信息
     */
    private String sendBatchPurchaseInspectionToSAP(List<WmsReceivedetail> detailsList, WmsReceive receive) {
        if (detailsList == null || detailsList.isEmpty()) {
            return null;
        }

        // 检查供应商编码是否为免检供应商
        String supplierCode = receive.getSupplierCode();
        if (isExemptInspectionSupplier(supplierCode)) {
            log.info("供应商 {} 是免检供应商，不需要发送送检申请", supplierCode);
            // 直接将明细的检验标记设置为免检(3)，而不是送检失败(4)
            updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.NO.getValue());
            return null;
        }

        try {
            // 获取采购送检接口配置
            SapApiConfig config = sapApiConfigService.getConfigByCode(PURCHASE_INSPECTION_CONFIG_CODE);
            if (config == null) {
                String errorMsg = "未找到采购送检接口配置，请检查数据库中是否存在configCode为" + PURCHASE_INSPECTION_CONFIG_CODE + "的记录";
                log.error(errorMsg);
                // 更新所有明细的检验标记为"送检失败"(4)
                updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.CHECK_FAIL.getValue());
                return errorMsg;
            }

            // 获取工作单号（采购订单号）
            String workNo = receive.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                String errorMsg = "采购送检申请失败，未关联采购订单号";
                log.error(errorMsg);
                // 更新所有明细的检验标记为"送检失败"(4)
                updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.CHECK_FAIL.getValue());
                return errorMsg;
            }

            List<WmsReceivedetail> notSentDetails = new ArrayList<>();
            for (WmsReceivedetail detail : detailsList) {
                // 检查每个物料是否已经有“送检中”或“已送检”状态
                if (!WmsConstant.CheckTagEnum.CHECKING.getValue().equals(detail.getCheckTag()) &&
                        !WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue().equals(detail.getCheckTag())) {
                    notSentDetails.add(detail);
                } else {
                    log.info("物料 {} 已经在送检中或已送检，跳过送检", detail.getItemNumber());
                }
            }

            if (notSentDetails.isEmpty()) {
                String infoMsg = "所有物料已送检或正在送检，无需再次发送";
                log.info(infoMsg);
                return infoMsg;
            }

            // 构建请求参数
            JSONObject requestParams = new JSONObject();

            // 表头信息
            requestParams.put("DBName", receive.getAccountCode()); // 目标账套代码

            // 直接从收货单获取供应商信息
            supplierCode = receive.getSupplierCode() != null ? receive.getSupplierCode() : "";
            String supplierName = receive.getSupplierName() != null ? receive.getSupplierName() : "";

            // 设置必填的表头信息
            requestParams.put("U_Z12_CDCD", supplierCode); // 供应商代码
            requestParams.put("U_Z12_CDNM", supplierName); // 供应商名称
            requestParams.put("U_Z12_CRUR", receive.getInspectionPerson()); // 送检人
            //格式化送检日期
            requestParams.put("U_Z12_CRDT", formatDate(receive.getInspectionDate())); // 送检日期

            // 设置明细行信息（Lines数组）
            JSONArray linesArray = new JSONArray();

            // 遍历所有需要送检的明细
            for (WmsReceivedetail detail : detailsList) {
                JSONObject lineItem = new JSONObject();

                // 按照规则组装订单标识：采购订单号*采购订单行号
                String orderIdentifier = workNo + "*" + detail.getLineNo();

                // 明细行数据
                lineItem.put("U_Z12_PDOC", workNo); // 采购订单号
                lineItem.put("U_Z12_SDEL", orderIdentifier); // 订单标识（采购订单号*采购订单行号）
                lineItem.put("U_Z12_IITM", detail.getItemNumber()); // 产品编号
                lineItem.put("U_Z12_INAM", detail.getItemName()); // 产品描述
                lineItem.put("U_Z12_PQTY", detail.getBatchQty()); // 送检数量

                linesArray.add(lineItem);
            }

            requestParams.put("Lines", linesArray);

            // 发送请求到SAP
            String url = mainBaseUrl + config.getEndpoint();
            JSONObject result = SapApiUtil.executeRequest(url, requestParams, "采购送检申请"+workNo, baseCommonService);
//            JSONObject result = SapApiUtil.mockRequest(requestParams,"采购送检申请"+workNo,  baseCommonService,null);

            // 处理响应结果
            if (result.getBooleanValue("result")) {
                log.info("批量采购送检申请发送成功，订单号：{}", workNo);
                // 更新所有明细的检验标记为"已申请"(1)
                updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.CHECKING.getValue());
                return null; // 成功返回null
            } else {
                String errorMsg = result.getString("msg");
                log.error("批量采购送检申请发送失败，订单号：{}，错误信息：{}", workNo, errorMsg);
                // 更新所有明细的检验标记为"送检失败"(4)
                updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.CHECK_FAIL.getValue());
                return errorMsg; // 返回SAP的错误信息
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("批量发送采购送检申请时发生异常", e);
            // 更新所有明细的检验标记为"送检失败"(4)
            updateCheckTagForDetails(detailsList, WmsConstant.CheckTagEnum.CHECK_FAIL.getValue());
            return "发送采购送检申请时发生异常: " + errorMsg; // 返回异常信息
        }
    }

    /**
     * 批量更新明细的检验标记
     * @param detailsList 明细列表
     * @param checkTag 检验标记值
     */
    private void updateCheckTagForDetails(List<WmsReceivedetail> detailsList, String checkTag) {
        if (detailsList == null || detailsList.isEmpty()) {
            return;
        }

        for (WmsReceivedetail detail : detailsList) {
            detail.setCheckTag(checkTag);
            wmsReceivedetailMapper.updateById(detail);
        }
    }

    /**
     * 格式化日期为yyyy-MM-dd格式的字符串
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    //根据品号查询物料信息
    private WmsSpecMatchItem getSpecMatchItemByItemNumber(String itemNumber,String accountCode){
        QueryWrapper<WmsSpecMatchItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_code", itemNumber);
        queryWrapper.eq("account_code", accountCode);
        return specMatchItemService.getOne(queryWrapper);
    }

    @Override
    public IPage<WmsReceive> queryReceiveByItem(Page<WmsReceive> page, String itemCode, String itemName, String batchCode, QueryWrapper<WmsReceive> queryWrapper) {
        // 检查是否需要根据 itemCode、itemName 或 batchCode 进行过滤
        if (StringUtils.isNotBlank(itemCode) || StringUtils.isNotBlank(itemName) || StringUtils.isNotBlank(batchCode)) {
            // 构建子表的查询条件
            QueryWrapper<WmsReceivedetail> detailQueryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(itemCode)) {
                detailQueryWrapper.eq("item_number", itemCode);
            }
            if (StringUtils.isNotBlank(itemName)) {
                detailQueryWrapper.like("item_name", itemName);
            }
            if (StringUtils.isNotBlank(batchCode)) {
                detailQueryWrapper.eq("batch_code", batchCode);
            }

            // 查询符合条件的 receiveId
            List<String> receiveIds = wmsReceivedetailMapper.selectList(detailQueryWrapper)
                    .stream()
                    .map(WmsReceivedetail::getBillId)
                    .distinct()
                    .collect(Collectors.toList());

            if (receiveIds.isEmpty()) {
                // 如果没有匹配的 receiveId，则返回空的分页结果
                return new Page<>();
            }

            // 将 receiveIds 作为主表的过滤条件
            queryWrapper.in("id", receiveIds);
        }

        // 执行主表的分页查询
        return this.page(page, queryWrapper);
    }

    /**
     * 手动重新推送SAP采购送检单
     * @param wmsReceivedetailList 收货明细列表
     * @return 包含操作结果和消息的Map，key为true或false，value为结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<Boolean, String> manualResendPurchaseInspection(List<WmsReceivedetail> wmsReceivedetailList) {
        return manualResendPurchaseInspection(wmsReceivedetailList, null, null, null);
    }

    /**
     * 手动重新推送SAP采购送检单（增强版，支持设置送检人、送检日期和送检数量）
     * @param wmsReceivedetailList 收货明细列表
     * @param inspectionPerson 送检人
     * @param inspectionDate 送检日期
     * @param inspectionQty 送检数量
     * @return 包含操作结果和消息的Map，key为true或false，value为结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<Boolean, String> manualResendPurchaseInspection(List<WmsReceivedetail> wmsReceivedetailList,
                                                String inspectionPerson,
                                                Date inspectionDate,
                                                Double inspectionQty) {
        Map<Boolean, String> resultMap = new HashMap<>();

        if (wmsReceivedetailList == null || wmsReceivedetailList.isEmpty()) {
            resultMap.put(false, "没有需要推送的明细数据");
            return resultMap;
        }

        // 验证所有明细是否属于同一个主单据
        String firstBillId = wmsReceivedetailList.get(0).getBillId();
        boolean isSameBill = wmsReceivedetailList.stream()
                .allMatch(detail -> firstBillId.equals(detail.getBillId()));

        if (!isSameBill) {
            resultMap.put(false, "所有明细必须属于同一个主单据");
            return resultMap;
        }

        // 验证明细的检验标记是否为送检失败(4)
        List<WmsReceivedetail> validDetails = wmsReceivedetailList.stream()
                .filter(detail -> WmsConstant.CheckTagEnum.CHECK_FAIL.getValue().equals(detail.getCheckTag()))
                .collect(Collectors.toList());

        if (validDetails.isEmpty()) {
            resultMap.put(false, "没有符合条件的明细数据（检验标记必须为'送检失败'）");
            return resultMap;
        }

        // 根据billId查询主单据
        WmsReceive wmsReceive = this.getById(firstBillId);
        if (wmsReceive == null) {
            resultMap.put(false, "未找到对应的主单据记录");
            return resultMap;
        }

        // 检查供应商是否为免检供应商
        if (isExemptInspectionSupplier(wmsReceive.getSupplierCode())) {
            // 将明细的检验标记设置为免检(3)
            updateCheckTagForDetails(validDetails, WmsConstant.CheckTagEnum.NO.getValue());
            resultMap.put(true, "供应商 " + wmsReceive.getSupplierCode() + " 是免检供应商，已将检验标记设置为免检");
            return resultMap;
        }

        try {
            // 设置送检信息
            if (inspectionPerson != null) {
                wmsReceive.setInspectionPerson(inspectionPerson);
            } else if (StringUtils.isBlank(wmsReceive.getInspectionPerson())) {
                // 如果未指定送检人且原记录中也没有，则尝试获取当前登录用户
                try {
                    LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                    if (loginUser != null) {
                        wmsReceive.setInspectionPerson(loginUser.getUsername());
                    }
                } catch (Exception e) {
                    log.warn("获取当前登录用户失败", e);
                }
            }

            if (inspectionDate != null) {
                wmsReceive.setInspectionDate(inspectionDate);
            } else if (wmsReceive.getInspectionDate() == null) {
                // 如果未指定送检日期且原记录中也没有，则使用当前日期
                wmsReceive.setInspectionDate(new Date());
            }

            if (inspectionQty != null) {
                wmsReceive.setInspectionQty(inspectionQty);
            } else if (wmsReceive.getInspectionQty() == null) {
                // 如果未指定送检数量且原记录中也没有，则设置默认值
                wmsReceive.setInspectionQty(0.2);
            }

            // 调用批量发送方法
            String result = sendBatchPurchaseInspectionToSAP(validDetails, wmsReceive);
            if (result == null) {
                resultMap.put(true, "手动推送SAP采购送检单成功");
                return resultMap;
            } else {
                resultMap.put(false, "手动推送SAP采购送检单失败: " + result);
                return resultMap;
            }
        } catch (Exception e) {
            log.error("手动推送SAP采购送检单失败", e);
            resultMap.put(false, "手动推送SAP采购送检单失败: " + e.getMessage());
            return resultMap;
        }
    }

    /**
     * 判断供应商是否为免检供应商
     * @param supplierCode 供应商编码
     * @return 是否为免检供应商
     */
    private boolean isExemptInspectionSupplier(String supplierCode) {
        if (StringUtils.isBlank(supplierCode)) {
            return false;
        }

        // 从功能配置表中查询免检供应商列表
        QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("field", "supplierFreeInspection");
        List<WmsFeatureConfiguration> configList = wmsFeatureConfigurationService.list(queryWrapper);

        if (configList == null || configList.isEmpty()) {
            log.info("未配置免检供应商列表");
            return false;
        }

        // 获取免检供应商配置值
        String exemptSuppliers = configList.get(0).getValue();
        if (StringUtils.isBlank(exemptSuppliers)) {
            return false;
        }

        // 将配置值按逗号分割成列表
        String[] supplierArray = exemptSuppliers.split(",");

        // 检查当前供应商是否在免检供应商列表中
        for (String supplier : supplierArray) {
            if (supplier.trim().equals(supplierCode)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public JSONObject getInspectionStatus() {
        // 创建返回结果对象
        JSONObject result = new JSONObject();

        // 查询最近30天的billType为采购入库的所有收货记录
        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_type", WmsConstant.ConttaskBillTypeEnum.CGRK.getValue());
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        queryWrapper.ge("create_time", DateUtils.addDays(new Date(), -1));
        List<WmsReceive> receiveList = this.list(queryWrapper);

        // 将所有的receiveList中的id放入一个list中
        List<String> receiveIdList = receiveList.stream().map(WmsReceive::getId).collect(Collectors.toList());

        // 初始化空的明细列表和分组Map
        List<WmsReceivedetail> detailList = new ArrayList<>();
        Map<String, List<WmsReceivedetail>> groupByCheckTag = new HashMap<>();

        // 只有当receiveIdList不为空时才进行查询
        if (!receiveIdList.isEmpty()) {
            // 根据receiveIdList查询所有的收货明细
            QueryWrapper<WmsReceivedetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.in("bill_id", receiveIdList);
            detailList = wmsReceivedetailMapper.selectList(detailQueryWrapper);

            // 按检验标记分组统计
            groupByCheckTag = detailList.stream()
                    .collect(Collectors.groupingBy(WmsReceivedetail::getCheckTag));
        }

        // 获取待检验(1)、已完成(2)、不合格(5)的批次数量
        int waitingCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECKING.getValue(), Collections.emptyList()).size();
        int completedCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue(), Collections.emptyList()).size();
        int failedCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_NOT_PASS.getValue(), Collections.emptyList()).size();

        // 计算合格率
        int totalCount = completedCount + failedCount;
        double passRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;

        // 计算平均检测用时（小时）
        double avgTestTime = calculateAvgTestTime(groupByCheckTag);

        // 获取昨天的统计数据，用于计算趋势
        JSONObject yesterdayStats = getYesterdayInspectionStats();

        // 计算趋势数据
        JSONObject waitingTrend = calculateTrend(waitingCount, yesterdayStats.getIntValue("waitingCount"));
        JSONObject completedTrend = calculateTrend(completedCount, yesterdayStats.getIntValue("completedCount"));
        JSONObject failedTrend = calculateTrend(failedCount, yesterdayStats.getIntValue("failedCount"));
        JSONObject passRateTrend = calculateTrend(passRate, yesterdayStats.getDoubleValue("passRate"));
        JSONObject avgTestTimeTrend = calculateTrend(avgTestTime, yesterdayStats.getDoubleValue("avgTestTime"), true);

        // 构建返回结果
        result.put("waitingCount", waitingCount);
        result.put("completedCount", completedCount);
        result.put("failedCount", failedCount);
        result.put("passRate", Math.round(passRate * 10) / 10.0); // 保留一位小数
        result.put("avgTestTime", Math.round(avgTestTime * 10) / 10.0); // 保留一位小数
        result.put("waitingTrend", waitingTrend);
        result.put("completedTrend", completedTrend);
        result.put("failedTrend", failedTrend);
        result.put("passRateTrend", passRateTrend);
        result.put("avgTestTimeTrend", avgTestTimeTrend);

        return result;
    }

    /**
     * 计算平均检测用时（小时）
     * @param groupByCheckTag 按检验标记分组的明细列表
     * @return 平均检测用时（小时）
     */
    private double calculateAvgTestTime(Map<String, List<WmsReceivedetail>> groupByCheckTag) {
        // 获取所有已完成检验的记录（包括已完成和不合格）
        List<WmsReceivedetail> completedDetails = new ArrayList<>();
        completedDetails.addAll(groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue(), Collections.emptyList()));
        completedDetails.addAll(groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_NOT_PASS.getValue(), Collections.emptyList()));

        if (completedDetails.isEmpty()) {
            return 0;
        }

        // 计算每条记录的检测用时（从创建时间到更新时间的小时数）
        double totalHours = 0;
        int count = 0;

        for (WmsReceivedetail detail : completedDetails) {
            if (detail.getCreateTime() != null && detail.getUpdateTime() != null) {
                // 计算时间差（毫秒）
                long diffMillis = detail.getUpdateTime().getTime() - detail.getCreateTime().getTime();
                // 转换为小时
                double hours = diffMillis / (1000.0 * 60 * 60);
                // 只统计合理的检测时间（大于0且小于7天）
                if (hours > 0 && hours < 168) { // 7天 = 168小时
                    totalHours += hours;
                    count++;
                }
            }
        }

        return count > 0 ? totalHours / count : 0;
    }

    /**
     * 获取昨天的检验统计数据
     * @return 昨天的统计数据
     */
    private JSONObject getYesterdayInspectionStats() {
        JSONObject yesterdayStats = new JSONObject();

        // 查询昨天的billType为采购入库的所有收货记录
        Date yesterday = DateUtils.addDays(new Date(), -1);
        Date dayBeforeYesterday = DateUtils.addDays(new Date(), -2);

        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_type", WmsConstant.ConttaskBillTypeEnum.CGRK.getValue());
        queryWrapper.ge("create_time", dayBeforeYesterday);
        queryWrapper.lt("create_time", yesterday);
        List<WmsReceive> receiveList = this.list(queryWrapper);

        if (receiveList.isEmpty()) {
            // 如果没有昨天的数据，返回默认值
            yesterdayStats.put("waitingCount", 0);
            yesterdayStats.put("completedCount", 0);
            yesterdayStats.put("failedCount", 0);
            yesterdayStats.put("passRate", 0.0);
            yesterdayStats.put("avgTestTime", 0.0);
            return yesterdayStats;
        }

        // 将所有的receiveList中的id放入一个list中
        List<String> receiveIdList = receiveList.stream().map(WmsReceive::getId).collect(Collectors.toList());

        // 初始化空的明细列表和分组Map
        List<WmsReceivedetail> detailList = new ArrayList<>();
        Map<String, List<WmsReceivedetail>> groupByCheckTag = new HashMap<>();

        // 只有当receiveIdList不为空时才进行查询
        if (!receiveIdList.isEmpty()) {
            // 根据receiveIdList查询所有的收货明细
            QueryWrapper<WmsReceivedetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.in("bill_id", receiveIdList);
            detailList = wmsReceivedetailMapper.selectList(detailQueryWrapper);

            // 按检验标记分组统计
            groupByCheckTag = detailList.stream()
                    .collect(Collectors.groupingBy(WmsReceivedetail::getCheckTag));
        }

        // 获取待检验(1)、已完成(2)、不合格(5)的批次数量
        int waitingCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECKING.getValue(), Collections.emptyList()).size();
        int completedCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue(), Collections.emptyList()).size();
        int failedCount = groupByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_NOT_PASS.getValue(), Collections.emptyList()).size();

        // 计算合格率
        int totalCount = waitingCount + completedCount + failedCount;
        double passRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;

        // 计算平均检测用时
        double avgTestTime = calculateAvgTestTime(groupByCheckTag);

        // 构建昨天的统计数据
        yesterdayStats.put("waitingCount", waitingCount);
        yesterdayStats.put("completedCount", completedCount);
        yesterdayStats.put("failedCount", failedCount);
        yesterdayStats.put("passRate", passRate);
        yesterdayStats.put("avgTestTime", avgTestTime);

        return yesterdayStats;
    }

    /**
     * 计算趋势数据
     * @param currentValue 当前值
     * @param previousValue 前一天的值
     * @param isReverse 是否反向计算（如检测时间，减少是好的趋势）
     * @return 趋势数据对象
     */
    private JSONObject calculateTrend(double currentValue, double previousValue, boolean... isReverse) {
        JSONObject trend = new JSONObject();
        boolean reverse = isReverse.length > 0 && isReverse[0];

        if (previousValue == 0) {
            // 如果前一天的值为0，无法计算百分比变化
            trend.put("type", currentValue > 0 ? (reverse ? "decrease" : "increase") : "increase");
            trend.put("value", 0);
            return trend;
        }

        double changePercent = (currentValue - previousValue) / previousValue * 100;
        boolean isIncrease = changePercent > 0;

        // 如果是反向计算（如检测时间），则增加是不好的趋势，减少是好的趋势
        if (reverse) {
            isIncrease = !isIncrease;
        }

        trend.put("type", isIncrease ? "increase" : "decrease");
        trend.put("value", Math.round(Math.abs(changePercent)));

        return trend;
    }

    @Override
    public List<WmsRecevieAndDetailPage> getPendingInspectionList() {
        //查询最近30天的billType为采购入库的所有收货记录
        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_type", WmsConstant.ConttaskBillTypeEnum.CGRK.getValue());
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        queryWrapper.ge("create_time", DateUtils.addDays(new Date(), -4));
        List<WmsReceive> receiveList = this.list(queryWrapper);

        // 如果没有收货记录，返回空列表
        if (receiveList.isEmpty()) {
            return Collections.emptyList();
        }

        //将所有的receiveList中的id放入一个list中
        List<String> receiveIdList = receiveList.stream().map(WmsReceive::getId).collect(Collectors.toList());

        //根据receiveIdList查询所有的收货明细
        QueryWrapper<WmsReceivedetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.in("bill_id", receiveIdList);
        List<WmsReceivedetail> detailList = wmsReceivedetailMapper.selectList(detailQueryWrapper);

        // 如果没有明细记录，返回空列表
        if (detailList.isEmpty()) {
            return Collections.emptyList();
        }

        // 将明细按检验标记分组
        Map<String, List<WmsReceivedetail>> detailsByCheckTag = detailList.stream()
                .collect(Collectors.groupingBy(WmsReceivedetail::getCheckTag));

        // 获取待检验(0)和检验中(1)的明细
        List<WmsReceivedetail> pendingDetails = new ArrayList<>(detailsByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECKING.getValue(), Collections.emptyList()));

        // 获取检验完成(2)和不合格(5)的明细
        List<WmsReceivedetail> completedDetails = new ArrayList<>();
        completedDetails.addAll(detailsByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue(), Collections.emptyList()));
        completedDetails.addAll(detailsByCheckTag.getOrDefault(WmsConstant.CheckTagEnum.CHECK_NOT_PASS.getValue(), Collections.emptyList()));

        // 创建一个Map，用于快速查找收货单据
        Map<String, WmsReceive> receiveMap = receiveList.stream()
                .collect(Collectors.toMap(WmsReceive::getId, receive -> receive));

        // 组装结果列表
        List<WmsRecevieAndDetailPage> resultList = new ArrayList<>();

        // 先处理待检验的明细
        for (WmsReceivedetail detail : pendingDetails) {
            WmsReceive receive = receiveMap.get(detail.getBillId());
            if (receive != null) {
                WmsRecevieAndDetailPage page = createRecevieAndDetailPage(receive, detail);
                resultList.add(page);
            }
        }

        // 再处理检验完成的明细
        for (WmsReceivedetail detail : completedDetails) {
            WmsReceive receive = receiveMap.get(detail.getBillId());
            if (receive != null) {
                WmsRecevieAndDetailPage page = createRecevieAndDetailPage(receive, detail);
                resultList.add(page);
            }
        }

        return resultList;
    }

    /**
     * 根据收货单据和明细创建WmsRecevieAndDetailPage对象
     * @param receive 收货单据
     * @param detail 收货明细
     * @return WmsRecevieAndDetailPage对象
     */
    private WmsRecevieAndDetailPage createRecevieAndDetailPage(WmsReceive receive, WmsReceivedetail detail) {
        WmsRecevieAndDetailPage page = new WmsRecevieAndDetailPage();
        page.setWorkNo(receive.getWorkNo()); // 采购订单号
        page.setSupplierName(receive.getSupplierName()); // 供应商
        page.setAccountCode(receive.getAccountCode()); // 账套信息

        // 格式化到货日期
        if (detail.getUpdateTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            page.setDeliveryDate(sdf.format(detail.getUpdateTime()));
        }

        page.setItemNumber(detail.getItemNumber()); // 品号
        page.setItemName(detail.getItemName()); // 品名
        page.setItemSpec(detail.getItemSpec()); // 规格
        page.setBatchCode(detail.getBatchCode()); // 批号
        page.setCheckTag(detail.getCheckTag()); // 检验标记

        return page;
    }
}
