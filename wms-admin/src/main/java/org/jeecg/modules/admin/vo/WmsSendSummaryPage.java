package org.jeecg.modules.admin.vo;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsSendSummary;
import org.jeecg.modules.admin.entity.WmsSendSummaryDetail;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 发货汇总
 * @Author: jeecg-boot
 * @Date:   2024-12-27
 * @Version: V1.0
 */
@Data
@ApiModel(value="wms_send_summaryPage对象", description="发货汇总")
public class WmsSendSummaryPage {

	/**主键*/
	@ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
	@ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**单据类型*/
	@Excel(name = "单据类型", width = 15, dicCode = "send_summary_type")
    @Dict(dicCode = "send_summary_type")
	@ApiModelProperty(value = "单据类型")
    private java.lang.String billType;
	/**单据名称*/
	@Excel(name = "单据名称", width = 15)
	@ApiModelProperty(value = "单据名称")
    private java.lang.String billName;
	/**发货暂存区域*/
	@Excel(name = "发货暂存区域", width = 15)
	@ApiModelProperty(value = "发货暂存区域")
    private java.lang.String sendTempStore;
	/**单据状态*/
	@Excel(name = "单据状态", width = 15, dicCode = "send_summary_status")
    @Dict(dicCode = "send_summary_status")
	@ApiModelProperty(value = "单据状态")
    private java.lang.String billStatus;
	/**账套信息*/
	@Excel(name = "账套信息", width = 15, dicCode = "account_code")
    @Dict(dicCode = "account_code")
	@ApiModelProperty(value = "账套信息")
    private java.lang.String accountCode;
	/**单据备注*/
	@Excel(name = "单据备注", width = 15)
	@ApiModelProperty(value = "单据备注")
    private java.lang.String remark;
	
	@ExcelCollection(name="出库汇总明细")
	@ApiModelProperty(value = "出库汇总明细")
	private List<WmsSendSummaryDetail> wmsSendSummaryDetailList;
	
}
