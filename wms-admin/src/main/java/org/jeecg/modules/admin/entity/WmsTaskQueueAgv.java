package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: AGV任务
 * @Author: jeecg-boot
 * @Date:   2024-08-20
 * @Version: V1.0
 */
@Data
@TableName("wms_task_queue_agv")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="wms_task_queue_agv对象", description="AGV任务")
public class WmsTaskQueueAgv implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**客户端编号*/
	@Excel(name = "客户端编号", width = 15)
    @ApiModelProperty(value = "客户端编号")
    private java.lang.String clientCode;
	/**任务号*/
	@Excel(name = "任务号", width = 15)
    @ApiModelProperty(value = "任务号")
    private java.lang.String taskNo;
	/**容器条码*/
	@Excel(name = "容器条码", width = 15)
    @ApiModelProperty(value = "容器条码")
    private java.lang.String containerBarcode;
	/**任务类型*/
	@Excel(name = "任务类型", width = 15, dicCode = "agv_task_type")
	@Dict(dicCode = "agv_task_type")
    @ApiModelProperty(value = "任务类型")
    private java.lang.String taskType;
	/**任务状态*/
	@Excel(name = "任务状态", width = 15, dicCode = "queue_agv_task_state")
	@Dict(dicCode = "queue_agv_task_state")
    @ApiModelProperty(value = "任务状态")
    private java.lang.String taskState;
	/**起始位置*/
	@Excel(name = "起始位置", width = 15)
    @ApiModelProperty(value = "起始位置")
    private java.lang.String sourcePos;
	/**目标位置*/
	@Excel(name = "目标位置", width = 15)
    @ApiModelProperty(value = "目标位置")
    private java.lang.String targetPos;
	/**优先级*/
	@Excel(name = "优先级", width = 15)
    @ApiModelProperty(value = "优先级")
    private java.lang.String priority;
	/**AGV任务类型*/
	@Excel(name = "AGV任务类型", width = 15)
    @ApiModelProperty(value = "AGV任务类型")
    private java.lang.String agvTaskType;
}
