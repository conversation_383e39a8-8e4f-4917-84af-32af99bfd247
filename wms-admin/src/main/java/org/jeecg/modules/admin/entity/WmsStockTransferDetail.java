package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 调拨单明细
 * @Author: jeecg-boot
 * @Date:   2025-01-04
 * @Version: V1.0
 */
@ApiModel(value="wms_stock_transfer_detail对象", description="调拨单明细")
@Data
@TableName("wms_stock_transfer_detail")
public class WmsStockTransferDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**调拨单id*/
    @ApiModelProperty(value = "调拨单id")
    private java.lang.String billId;
	/**调拨单编号*/
	@Excel(name = "调拨单编号", width = 15)
    @ApiModelProperty(value = "调拨单编号")
    private java.lang.String billNo;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemCode;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**物料条码*/
	@Excel(name = "物料条码", width = 15)
    @ApiModelProperty(value = "物料条码")
    private java.lang.String itemBarcode;
	/**库存类型*/
	@Excel(name = "库存类型", width = 15, dicCode = "stock_type")
	@Dict(dicCode = "stock_type")
    @ApiModelProperty(value = "库存类型")
    private java.lang.String stockType;
	/**库存数量*/
	@Excel(name = "库存数量", width = 15)
    @ApiModelProperty(value = "库存数量")
    private java.lang.Double stockQty;
	/**调拨数量*/
	@Excel(name = "调拨数量", width = 15)
    @ApiModelProperty(value = "调拨数量")
    private java.lang.Double transferQty;
	/**库存id*/
	@Excel(name = "库存id", width = 15)
    @ApiModelProperty(value = "库存id")
    private java.lang.String stockId;
}
