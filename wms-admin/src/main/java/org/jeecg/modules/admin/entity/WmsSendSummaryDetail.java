package org.jeecg.modules.admin.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 出库汇总明细
 * @Author: jeecg-boot
 * @Date:   2024-12-27
 * @Version: V1.0
 */
@ApiModel(value="wms_send_summary_detail对象", description="出库汇总明细")
@Data
@TableName("wms_send_summary_detail")
public class WmsSendSummaryDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String billNo;
	/**单据ID*/
    @ApiModelProperty(value = "单据ID")
    private java.lang.String billId;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemNumber;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**计划出库数量*/
	@Excel(name = "计划出库数量", width = 15)
    @ApiModelProperty(value = "计划出库数量")
    private java.lang.Double planQty;
	/**已配货数量*/
	@Excel(name = "已配货数量", width = 15)
    @ApiModelProperty(value = "已配货数量")
    private java.lang.Double disQty;
	/**已出库数量*/
	@Excel(name = "已出库数量", width = 15)
    @ApiModelProperty(value = "已出库数量")
    private java.lang.Double actQty;
	/**单据明细状态*/
	@Excel(name = "单据明细状态", width = 15, dicCode = "send_line_state")
	@Dict(dicCode = "send_line_state")
    @ApiModelProperty(value = "单据明细状态")
    private java.lang.String lineState;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
    /**仓库编号*/
    @Excel(name = "仓库编号", width = 15)
    @ApiModelProperty(value = "仓库编号")
    private java.lang.String warehouseCode;
}
