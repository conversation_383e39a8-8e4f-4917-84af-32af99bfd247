package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsStockTransferDetail;
import org.jeecg.modules.admin.mapper.WmsStockTransferDetailMapper;
import org.jeecg.modules.admin.service.IWmsStockTransferDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 调拨单明细
 * @Author: jeecg-boot
 * @Date:   2025-01-04
 * @Version: V1.0
 */
@Service
public class WmsStockTransferDetailServiceImpl extends ServiceImpl<WmsStockTransferDetailMapper, WmsStockTransferDetail> implements IWmsStockTransferDetailService {
	
	@Autowired
	private WmsStockTransferDetailMapper wmsStockTransferDetailMapper;
	
	@Override
	public List<WmsStockTransferDetail> selectByMainId(String mainId) {
		return wmsStockTransferDetailMapper.selectByMainId(mainId);
	}
}
