package org.jeecg.modules.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 回退
 * @Author: jeecg-boot
 * @Date:   2024-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value="BoundBackPage", description="回退")
public class BoundBackPage {

	/**单据号*/
	@ApiModelProperty(value = "单据号")
    private String bill_no;
}
