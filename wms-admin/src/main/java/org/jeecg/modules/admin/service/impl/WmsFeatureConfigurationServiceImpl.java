package org.jeecg.modules.admin.service.impl;

import org.jeecg.modules.admin.entity.WmsFeatureConfiguration;
import org.jeecg.modules.admin.mapper.WmsFeatureConfigurationMapper;
import org.jeecg.modules.admin.service.IWmsFeatureConfigurationService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 功能配置
 * @Author: jeecg-boot
 * @Date:   2024-07-11
 * @Version: V1.0
 */
@Service
public class WmsFeatureConfigurationServiceImpl extends ServiceImpl<WmsFeatureConfigurationMapper, WmsFeatureConfiguration> implements IWmsFeatureConfigurationService {

}
