package org.jeecg.modules.admin.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 采购订单明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
@ApiModel(value="wms_purchase_detail对象", description="采购订单明细")
@Data
@TableName("wms_purchase_detail")
public class WmsPurchaseDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**单据号*/
	@Excel(name = "单据号", width = 15)
    @ApiModelProperty(value = "单据号")
    private java.lang.String workNo;
	/**单据ID*/
    @ApiModelProperty(value = "单据ID")
    private java.lang.String workId;
	/**行号*/
	@Excel(name = "行号", width = 15)
    @ApiModelProperty(value = "行号")
    private java.lang.Integer lineNo;
	/**品号*/
	@Excel(name = "品号", width = 15)
    @ApiModelProperty(value = "品号")
    private java.lang.String itemNumber;
	/**品名*/
	@Excel(name = "品名", width = 15)
    @ApiModelProperty(value = "品名")
    private java.lang.String itemName;
	/**批号*/
	@Excel(name = "批号", width = 15)
    @ApiModelProperty(value = "批号")
    private java.lang.String batchCode;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private java.lang.String itemSpec;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
    private java.lang.String itemUnit;
	/**采购数量*/
	@Excel(name = "采购数量", width = 15)
    @ApiModelProperty(value = "采购数量")
    private java.lang.Double buyQty;
    /**已打印次数*/
    @Excel(name = "已打印次数", width = 15)
    @ApiModelProperty(value = "已打印次数")
    private java.lang.Integer printedNumber;
    /**已打印数量*/
    @Excel(name = "已打印数量", width = 15)
    @ApiModelProperty(value = "已打印数量")
    private java.lang.Double printedQty;
    /**上次打印时间*/
    @Excel(name = "上次打印时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上次打印时间")
    private java.util.Date printedTime;
    /**单据标识*/
    @Excel(name = "单据标识", width = 15)
    @ApiModelProperty(value = "单据标识")
    private java.lang.Integer serialNumber;
    /**仓库编号*/
    @Excel(name = "仓库编号", width = 15)
    @ApiModelProperty(value = "仓库编号")
    private java.lang.String warehouseCode;
    /**仓库名称*/
    @Excel(name = "仓库名称", width = 15)
    @ApiModelProperty(value = "仓库名称")
    @TableField(exist = false)
    private java.lang.String warehouseName;
    /**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
}
