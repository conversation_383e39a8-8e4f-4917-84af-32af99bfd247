package org.jeecg.modules.admin.mapper;

import java.util.List;
import org.jeecg.modules.admin.entity.WmsPurreturnDraftDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 采购退货草稿明细
 * @Author: jeecg-boot
 * @Date:   2024-09-27
 * @Version: V1.0
 */
public interface WmsPurreturnDraftDetailMapper extends BaseMapper<WmsPurreturnDraftDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<WmsPurreturnDraftDetail>
   */
	public List<WmsPurreturnDraftDetail> selectByMainId(@Param("mainId") String mainId);
}
